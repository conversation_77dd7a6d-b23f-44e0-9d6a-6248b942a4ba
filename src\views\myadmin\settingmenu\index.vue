<template>
  <!-- 系统日志 -->
  <div>
    <div class="cz_header">
      <Input style="width:15%;margin-right:10px;" placeholder="可输入配置键查询信息" v-model="search_value" @on-enter="getmanagementList" />
      <Select v-model="status" style="width:200px" clearable placeholder="请选择状态">
        <Option value="启用">启用</Option>
        <Option value="禁用">禁用</Option>
      </Select>
      <Button type="primary" icon="ios-search" @click.native="getmanagementList">查询</Button>
      <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      <Button style="margin-left:10px;float:right;" :disabled="hasEditPermission" type="success" ghost @click="addsetting" icon="md-add">新建配置</Button>
      </Button>
    </div>
    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData">
        <template slot-scope="{ row, index }" slot="action">
          <Button size="small" :style="buttonStyle" :disabled="hasEditPermission" @click="edit(row)">修改</Button>
          <Poptip title="确认删除吗？" confirm v-show="row.status == '禁用'" @on-ok="remove(row.id)">
            <Button size="small" :disabled="hasEditPermission" :style="buttonStyle">删除</Button>
          </Poptip>
          <Button size="small" style="margin-right: 5px;color:#3D73EF;border:1px solid #3D73EF;" @click="showlog(row.id)">日志查看</Button>
        </template>
      </Table>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" show-sizer show-total show-elevator class="paging" @on-change="changepage" :current.sync="tablePageParam.pageIndex" style="text-align: right;margin-top:20px;" @on-page-size-change="pageSizeChange" :pageSizeOpts="pageSizeOpts"></Page>
    </div>
    <Modal v-model="modal1" :mask-closable="false" :title="modal1title" @on-cancel="closeAll">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
        <FormItem label="配置键" prop="name">
          <Input v-model.trim="formValidate.name" show-word-limit maxlength="32" placeholder="请输入配置键"></Input>
        </FormItem>
        <FormItem label="配置值" prop="value" tabindex="0">
          <Input v-model.trim="formValidate.value" @on-focus="listenfocusone" @on-blur="listenblurone" :class="{input_reset_css_focus:focusone==1}" class="valueinput input_reset_css" show-word-limit maxlength="2048" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请输入配置值，多个配置值用英文,隔开。例如，单个手机号，需填入18434364814，多个手机号，则需填入184343656565,18434367788。"></Input>
        </FormItem>
        <FormItem label="描述" prop="description">
          <Input v-model.trim="formValidate.description" @on-focus="listenfocustwo" @on-blur="listenblurtwo" :class="{input_reset_css_focus:focustwo==1}" class="valueinput input_reset_css" show-word-limit maxlength="256" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请输入描述"></Input>
        </FormItem>
        <FormItem label="状态" prop="status">
          <Select v-model="formValidate.status" placeholder="请选择状态">
            <Option value="ENABLE">启用</Option>
            <Option value="DISABLE">禁用</Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button size="small" @click="closeAll">取消</Button>
        <Button type="primary" size="small" @click="handleSubmit('formValidate')">确定</Button>
      </div>
    </Modal>
    <Modal v-model="modal2" title="日志查看" @on-ok="closeAll" @on-cancel="closeAll">
      <Table style="max-height:300px;overflow-y:auto;overflow-x:hidden;" :columns="columns1" :data="data1"></Table>
    </Modal>
    <Modal v-model="modal3" :styles="{top: '250px'}" title="修改确认">
      <p style="text-align:center;">点击确认后，系统会对数据进行更新!</p>
      <div slot="footer">
        <Button size="small" @click="closemodal3">取消</Button>
        <Button type="primary" size="small" @click="addconfig(formValidate)">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { addconfig, managementList, blogList, deleteconfig } from '@/api/contract'
import { localRead } from '@/lib/util'
export default {
  data () {
    return {
      focusone: 0,
      focustwo: 0,
      pageSizeOpts: [10, 20, 40, 60, 100],
      isdisabled: false,
      formValidate: {// form表单
        id: '',
        name: '',
        value: '',
        description: '',
        status: ''
      },
      ruleValidate: {// 字段规则
        name: [
          { required: true, message: '配置键为必填', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '配置值为必填', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '描述为必填', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态为必填', trigger: 'change' }
        ]
      },
      columns1: [// table
        {
          title: '操作人',
          key: 'userLoginId'
        },
        {
          title: '操作时间',
          key: 'operateTime'
        },
        {
          title: '操作类型',
          key: 'operateType'
        },
        {
          title: '操作说明',
          key: 'operateDescribe'
        }
      ],
      data1: [], // table数据
      modal1: false, // 弹窗
      modal2: false,
      modal3: false,
      status: '', // 搜索下拉框值
      dataCount: 0, // 总条数
      search_value: '', // 输入框
      tablePageParam: { pageIndex: 1, pageSize: 10 }, // 分页
      historyColumns: [//   table 表头
        {
          title: '配置键',
          key: 'name',
        },
        {
          title: '配置值',
          key: 'value',
          tooltip: true

          // render: (h, params) => {
          //     let texts = '';                                       //表格列显示文字

          //     if (params.row.value!== null&&params.row.value.length > 8) {                    //进行截取列显示字数
          //         texts = params.row.value.substring(0, 8) + "...";
          //     } else {
          //         texts = params.row.value;
          //     }
          //     // console.log(texts,"字段")
          //     return h('Tooltip', {
          //         props: {
          //                 placement: 'bottom'
          //         }
          //     }, [
          //         texts,
          //         h('span', {
          //             slot: 'content',
          //             style: {
          //                 whiteSpace: 'normal',
          //                 wordBreak: 'break-all',
          //                 width:'200'
          //             }
          //         }, params.row.value)
          //     ])
          // }

        },
        // {
        //   title: '描述',
        //   key: 'description',
        // },
        {
          title: '状态',
          key: 'status'
        },
        {
          title: '创建人',
          key: 'uname'
        },
        {
          title: '创建时间',
          key: 'createTime'
        },
        {
          title: '更新时间',
          key: 'updateTime'
        },
        {
          title: '操作',
          key: 'resultCode',
          slot: 'action',
          width: '180'
        }
      ],
      // 表格数据
      historyData: [],
      olddata: {},
      isedit: false,
      modal1title: '新增配置',
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  mounted () {
    this.getmanagementList()
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 监听input框focus事件
    listenfocusone () {
      this.focusone = 1
    },
    listenfocustwo () {
      this.focustwo = 1
    },
    listenblurone () {
      this.focusone = 0
    },
    listenblurtwo () {
      this.focustwo = 0
    },
    // 取消
    closemodal3 () {
      this.modal3 = false
    },
    // 打开新增配置弹窗
    addsetting () {
      this.isedit = false
      this.modal1title = '新增配置'
      this.isdisabled = false
      this.modal1 = true
    },
    // 查询列表接口
    getmanagementList () {
      let params = {
        name: this.search_value, // 键名
        status: this.status, // 状态
        pageParam: this.tablePageParam // 分页
      }
      managementList(params).then((res) => {
        this.dataCount = res.data.total
        this.historyData = res.data.records
        this.historyData.map(item => {
          if (item.name == 'RSA_PRIVATE_KEY') {
            item.value = '********'
          }
        })
      }).catch(error => {
        // console.log('addChainDeploy.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 表单验证
    handleSubmit (name) {
      let that = this
      that.$refs[name].validate((valid) => {
        if (valid) {
          // console.log("判断2222",Object.is(that.formValidate,that.olddata))
          if (that.formValidate.id == that.olddata.id &&
            that.formValidate.name == that.olddata.name &&
            that.formValidate.value == that.olddata.value &&
            that.formValidate.description == that.olddata.description &&
            that.formValidate.status == that.olddata.status) {
            that.$Message.warning('数据无更新，无须重复提交！')
          } else {
            if (this.isedit) {
              that.modal3 = true
            } else {
              that.addconfig(that.formValidate)
            }
          }
        }
      })
    },
    // 确定新增修改配置
    async addconfig (param) {
      await addconfig(param).then((res) => {
        if (res.code == '00000') {
          this.closeAll()
          this.$Message.success(res.message)
        } else {
          this.$Message.error(res.message)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      await this.getmanagementList()
    },
    // 编辑配置
    edit (row) {
      this.isedit = true
      this.modal1title = '修改配置'
      this.formValidate = {
        id: row.id,
        name: row.name,
        value: row.value,
        description: row.description,
        status: row.status == '启用' ? 'ENABLE' : 'DISABLE'
      }
      this.olddata = {
        id: row.id,
        name: row.name,
        value: row.value,
        description: row.description,
        status: row.status == '启用' ? 'ENABLE' : 'DISABLE'
      }
      this.isdisabled = true
      this.modal1 = true
    },
    // 查看日志
    showlog (id) {
      this.data1 = []
      blogList({ configId: id }).then(res => {
        this.data1 = res.data
        this.modal2 = true
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 删除配置
    async remove (id) {
      await deleteconfig(id).then(res => { this.$Message.success(res.message) }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      await this.getmanagementList()
    },
    // 关闭所有弹窗清空所有值
    closeAll () {
      this.modal1 = false
      this.modal2 = false
      this.modal3 = false
      this.focusone = 0
      this.focustwo = 0
      this.$refs.formValidate.resetFields()
      this.formValidate.id = ''
    },
    // 翻页
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getmanagementList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getmanagementList()
    },
    // 重置按钮事件
    reset () {
      this.search_value = ''
      this.status = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getmanagementList()
    }

  }

}
</script>

<style lang="less" scoped>
/deep/ .valueinput {
  textarea.ivu-input {
    padding-bottom: 15px;
    min-height: 80px !important;
  }
}
.cz_header {
  // display: flex;
  margin-top: 10px;
  /deep/ .ivu-select,
  /deep/ .ivu-date-picker {
    width: 15%;
    margin-right: 10px;
  }
}

//
/deep/ .ivu-modal {
  width: 700px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
//
.cz_gjz {
  height: 33px;
  line-height: 32px;
  width: 99px;
  text-align: center;
  border: 1px solid;
  background: #2d8cf0;
  color: #fff;
}
.ivu-btn-primary {
  margin-left: 7px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 50%;
}
/deep/ .ivu-table-wrapper {
  overflow: visible;
}

/deep/ .ivu-btn-small:hover {
  background-color: rgba(61, 115, 239, 0.8) !important;
  color: #fff !important;
}
/deep/ .ivu-btn-small:active {
  background-color: #3d73ef;
}

/deep/.ivu-tooltip-inner {
  max-width: 400px;
  z-index: 10000;
}
/deep/ .ivu-table-overflowX {
  overflow-x: hidden;
}
/deep/ .ivu-table:before {
  display: none;
}

// 弹窗样式
.input_reset_css {
  border: 1px solid #dcdee2;
  padding-bottom: 20px;
  border-radius: 4px;
  /deep/ .ivu-input {
    border: none !important;
  }
  /deep/ .ivu-input:focus {
    border: none !important;
    box-shadow: 0px 0px 0px #fff !important;
  }
}

.input_reset_css_focus {
  border-color: #57a3f3;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
/deep/ .ivu-form-item-error {
  .input_reset_css {
    border: 1px solid red;
  }
  // .input_reset_css{
  //     box-shadow:0 0 0 2px rgba(237,64,20,0.2);
  // }
  .input_reset_css_focus {
    //  border: 1px solid red;
    box-shadow: 0 0 0 2px rgba(237, 64, 20, 0.2);
  }
}
</style>
<style lang="less">
// .ivu-tooltip-popper{
//     max-height: 400px!important;
//     overflow-y:auto;
// }
.ivu-tooltip-contentr {
  max-height: 400px;
  overflow-y: auto;
}
.ivu-tooltip-inner {
  max-height: 300px;
  overflow-y: auto;
}
</style>
