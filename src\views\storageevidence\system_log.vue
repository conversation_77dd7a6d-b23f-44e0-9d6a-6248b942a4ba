<template>
  <!-- 系统日志 -->
  <div>
    <div class="cz_header">
      <div class="cz_ss">
        <Input placeholder="请输入用户名称" v-model="search_value" @on-enter="uservalue" />
      </div>

      <Row>
        <div class="sl_timout">安全级别</div>
        <Col span="12">
        <Select v-model="security_level" style="width: 100px" placeholder="安全">
          <Option v-for="item in SecurityList" :value="item.value" :key="item.value" @click.native="get_Type(item.value)">{{ item.label }}</Option>
        </Select>
        </Col>
      </Row>
      <!--  -->
      <Row>
        <div class="sl_timout">日志时间</div>
        <Col span="12">
        <DatePicker format="yyyy-MM-dd" type="daterange" placement="bottom-end" v-model="daterange" :editable='false' placeholder="开始日期~结束日期" style="width: 200px" @on-change="timeout_click"></DatePicker>
        </Col>
        <Button type="primary" icon="ios-search" @click.native="information">搜索</Button>
        <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      </Row>
    </div>
    <!-- table -->
    <div class="cz_table">
      <edit-table-mul :columns="historyColumns" v-model="historyData" :key="transferKey"></edit-table-mul>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" :current.sync="tablePageParam.pageIndex" show-sizer show-total show-elevator class="paging" @on-change="changepage" style="text-align: right;margin-top: 10px;" @on-page-size-change="pageSizeChange"></Page>
    </div>
  </div>
</template>
<script>
import { interfaceo } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  name: 'system_log',
  components: {
    EditTableMul
  },
  data () {
    return {
      security_level: 0,
      SecurityList: [
        {
          value: '10',
          label: '安全'
        },
        {
          value: '11',
          label: '危险'
        }
      ],
      // 初始化信息总条数
      dataCount: 0,
      transferKey: 0,
      // 上链时间
      chain_time: [],
      // 输入框
      search_value: '',
      // 下拉框
      lable: '',
      // 分页
      tablePageParam: { pageIndex: 1, pageSize: 10 },
      //   table 表头
      historyColumns: [
        {
          title: '序号',
          type: 'index',
        },
        {
          title: '用户',
          key: 'userLoginId'
        },
        {
          title: '请求真实url',
          key: 'requestUrl',
        },
        {
          title: '系统格式url',
          key: 'patternUrl',
        },
        {
          title: '请求参数',
          key: 'requestInfo',
          tooltip: true
        },
        {
          title: '返回参数',
          key: 'responseInfo',
          tooltip: true
        },
        {
          title: '返回码',
          key: 'resultCode'
        },
        {
          title: '安全级别',
          key: 'status',
          render: (h, params) => {
            if (params.row.status === '危险') {
              return h(
                'span',
                {
                  style: {
                    color: 'red'
                  }
                },
                params.row.status
              )
            } else {
              return h('span', {}, params.row.status)
            }
          }
        },
        {
          title: '操作时间',
          key: 'requestTime',
        }
      ],
      // 日期
      daterange: '',
      // 表格数据
      historyData: [],
      // 开始时间
      beginTime: '',
      // 结束时间
      endTime: ''
    }
  },
  methods: {
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getInterfaceo()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getInterfaceo()
    },
    // 重置事件
    reset () {
      this.search_value = ''
      this.daterange = ''
      this.beginTime = ''
      this.endTime = ''
      this.security_level = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getInterfaceo()
    },
    // 安全级别
    get_Type (e) {
      this.security_level = e
    },
    // 输入框值
    information () {
      this.search_value = this.search_value
      this.getInterfaceo()
    },
    // 回车时间
    // 回车事件
    uservalue () {
      this.getInterfaceo()
    },
    // 日志时间
    timeout_click (e) {
      this.beginTime = e[0]
      this.endTime = e[1]
    },
    getInterfaceo () {
      let intData = {
        userLoginId: this.search_value, // 用户id
        pageParam: this.tablePageParam, // 分页
        beginTime: this.beginTime, // 开始时间
        endTime: this.endTime, // 结束时间
        status: +this.security_level // 安全级别
      }
      interfaceo(intData).then((res) => {
        let security = {
          10: '安全',
          11: '危险'
        }
        let datas = res.data.records.map((item) => {
          return {
            ...item,
            status: security[item.status]
          }
        })
        this.dataCount = res.data.total
        this.historyData = datas
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },

  created () {
    this.getInterfaceo()
  }
}
</script>

<style lang="less" scoped>
.ivu-row {
  margin-right: 5px;
  display: flex;
  flex-flow: row wrap;
}
.ivu-card-body {
  padding: 20px;

  .ivu-select-single .ivu-select-selection {
    background: red;
  }
}
.cz_header {
  display: flex;
  margin-top: 10px;
  .sl_timout {
    height: 33px;
    padding: 5px 8px;
    text-align: center;
    border-radius: 4px;
  }
  .cz_sltimout {
    width: 50%;
  }
  .cz_ss {
    display: flex;
    margin-right: 5px;
  }
  //   .ivu-table-wrapper {
  //  overflow: visible;
  //   }
  //   .ivu-table{
  //      overflow: visible;
  //   }
}

// table
.cz_table {
  margin-top: 2% !important;
}
//
.cz_gjz {
  height: 33px;
  line-height: 32px;
  width: 99px;
  text-align: center;
  border: 1px solid;
  background: #2d8cf0;
  color: #fff;
}
.ivu-btn-primary {
  margin-left: 7px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 50%;
}
</style>
