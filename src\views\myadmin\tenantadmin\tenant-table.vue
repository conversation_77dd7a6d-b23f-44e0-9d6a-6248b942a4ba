<template>
  <div class="tenantadmin">
    <p style="text-align:right;">
      <b style="float:left;margin: 10px;">租户管理</b>
      <Input style="width:auto;vertical-align:baseline;" placeholder="可输入租户名称查询信息" v-model="inputvalue" @keyup.enter.native="searchList">
      <Icon type="ios-search" slot="suffix" @click="searchList" />
      </Input>
      <!-- <Button icon="ios-search" type="primary" @click="searchList">查询</Button> -->
      <Button style="margin:0 5px 0 10px;" type="success" ghost @click="createTenant" :disabled="hasEditPermission" icon="md-add">新建租户</Button>
      <Tooltip content="公司配置" placement="bottom-end" :transfer="true" class="tooltip-style">
        <img src="../../../assets/img/gs.png" alt="" style="position: relative;top: 11px;width: 30px;left: 0px;cursor:pointer" @click="addCompany">
      </Tooltip>

    </p>
    <edit-table-mul style="margin: 10px 0;" :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;" />
    <Modal v-model="modal" :title="arrTenant.alertTitle" :draggable="true" :mask-closable="false" sticky class="alertstyle">
      <Form ref="formItem" :rules="formItemRule" :model="arrTenant" :label-width="100" @submit.native.prevent>
        <FormItem label="租户名称：" prop="tenantName">
          <Input placeholder="填写租户名称" style="width:370px" v-model="arrTenant.tenantName" />
        </FormItem>
        <FormItem label="归属公司：" prop="company">
          <Select v-model="arrTenant.company" placeholder="请选择租户归属公司" style="width:370px" filterable>
            <Option v-for="item in selectList" :value="item.id" :key="item.id">{{item.companyName}}</Option>
          </Select>
        </FormItem>
        <FormItem label="租户描述：" prop="tenantBrief">
          <Input type="textarea" style="width:370px" v-model="arrTenant.tenantBrief" :autosize="{minRows: 3,maxRows: 5}" placeholder="填写租户描述" :maxlength="50" show-word-limit />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="cancel">取消</Button>
        <Button type="primary" @click="ok">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { addTenantPower, getTenantTableData } from '@/api/data'
import { tenantcompanyList } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import { localRead } from '@/lib/util'
// import { Input } from 'element-ui'
export default {
  name: 'tenant_admin',
  components: {
    EditTableMul
  },
  data () {
    return {
      modal: false,
      transferKey: 0,
      inputvalue: '',
      arrTenant: { alertTitle: '新建租户', tenantId: '', tenantName: '', tenantBrief: '', company: '' },
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      columns: [
        { key: 'tenantUuid', title: '租户唯一标识' },
        { key: 'tenantName', title: '租户名称' },
        { key: 'companyName', title: '归属公司' },
        { key: 'createTime', title: '创建时间' },
        { key: 'tenantBrief', title: '租户描述' },
        { // key: 'action',fixed: 'right',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => { this.showDetails(params.index) } }
              }, '详情'),
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: { click: () => { this.editTenant(params.index) } },

              }, '修改')
            ])
          }
        }
      ],
      tableData: [],
      formItemRule: {
        tenantName: [{ required: true, message: '不能为空', trigger: 'blur' },
        { max: 15, message: '不能多于15位', trigger: 'blur' }],
        tenantBrief: [{ required: true, message: '不能为空', trigger: 'blur' },
        { max: 50, message: '不能多于50位', trigger: 'blur' }],
        company: [
          { required: true, message: '请选择租户归属公司' }
        ]
      },
      userPermission: JSON.parse(localRead('userPermission')),
      // s9新增
      selectList: [] // 归属公司

    }
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    // 公司配置
    addCompany () {
      this.$router.push({
        name: 'new_company'
      })
      // this.companyModal = true
      // this.companyTable()
    },
    // 归属公司
    attribution () {
      let listcompany = {
        companyName: ''
      }
      tenantcompanyList(listcompany).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    createTenant () {
      this.init()
      this.modal = true
      this.attribution()
    },
    init () {
      // this.$nextTick(() => {
      this.$refs['formItem'] && this.$refs['formItem'].resetFields()
      // })
      this.arrTenant = { alertTitle: '新建租户', tenantId: '', tenantName: '', tenantBrief: '', company: '' }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    ok () {
      this.$refs['formItem'].validate((valid) => {
        if (valid) {
          console.log(this.arrTenant)
          addTenantPower(this.arrTenant.tenantName, this.arrTenant.tenantBrief, this.arrTenant.tenantId, this.arrTenant.company).then(res => {
            // console.log('addTenantPower===>', res)
            if (res.code !== '00000') {
              if (res.code === '500') {
                this.msgInfo('error', res.message, true)
              } else {
                this.msgInfo('warning', res.message, true)
              }
            } else {
              if (this.arrTenant.alertTitle === '新建租户') {
                this.msgInfo('success', '请管理员分配本租户的可见链', true)
              } else {
                this.msgInfo('success', res.message, true)
              }
              this.modal = false
              this.getTableData()
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.msgInfo('error', '内容不符合规范，请检查', true)
        }
      })
      // if (this.arrTenant.tenantName && this.arrTenant.tenantBrief) {
      //   addTenantPower(this.arrTenant.tenantName, this.arrTenant.tenantBrief, this.arrTenant.tenantId).then(res => {
      //     // console.log('addTenantPower===>', res)
      //     if (res.code !== '00000') this.msgInfo('warning', res.message, true)
      //     else this.getTableData()
      //   }).catch(error => {
      //     console.log('addTenantPower.error===>', error)
      //     this.msgInfo('error', error.message, true)
      //   })
      //   this.init()
      // } else {
      //   this.msgInfo('warning', '内容不能为空！', true)
      //   setTimeout(() => { this.modal = true }, 500)
      //   return false
      // }
    },
    cancel () { this.modal = false },
    // handleEdit ({ row, index, column, newValue }) {
    //   // console.log('handleEdit~~', index, this.tableData[index].tenantName, newValue)
    //   reviseTenantBrief(this.tableData[index].tenantId, this.tableData[index].tenantName, newValue).then(res => {
    //     // console.log('reviseTenantBrief===>', res)
    //     this.msgInfo('info', res.message)
    //   }).catch(error => {
    //     console.log('reviseTenantBrief.error===>', error)
    //     this.msgInfo('error', error.message, true)
    //   })
    // },
    editTenant (index) {
      this.init()
      this.attribution()
      if (`${this.tableData[index].tenantId}`) {
        this.arrTenant = {
          alertTitle: '修改租户',
          tenantId: `${this.tableData[index].tenantId}`,
          tenantName: `${this.tableData[index].tenantName}`,
          tenantBrief: `${this.tableData[index].tenantBrief}`,
          company: this.tableData[index].companyId
        }
        this.modal = true
      }
    },
    showDetails (index) {
      // console.log('showDetails~~', index)
      this.$router.push({
        name: 'tenant_details',
        params: {
          tenantId: `${this.tableData[index].tenantId}`
        }
      })
    },
    getTableData (inputvalue) {
      getTenantTableData(this.tablePageParam, this.inputvalue).then(res => {
        // console.log('getTenantTableData===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        }
      }).catch(error => {
        console.log('getTenantTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    searchList () { this.getTableData(this.inputvalue) }
  },
  mounted () {
    this.getTableData()
  },
  deactivated () {
    this.init()
    this.modal = false
  }
}
</script>

<style lang="less" scoped>
.tooltip-style {
  /deep/.ivu-tooltip-popper {
    cursor: default;
    top: 50px !important;
  }
}
input {
  margin: 0 0 10px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
