<template>
  <!-- ipfs -->
  <div>
    <div class="cz_header">
      <Input style="width:15%;margin-right:10px;" placeholder="请输入项目名称进行查询" v-model="search_value" @on-enter="getmanagementList" />
      <Button type="primary" icon="ios-search" @click.native="getmanagementList">查询</Button>
      <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      <Button style="margin-left:10px;float:right;" type="success" ghost @click="addipfs" :disabled="hasEditPermission" icon="md-add">新建项目</Button>
    </div>
    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData">
        <template slot-scope="{ row, index }" slot="action">
          <Button size="small" :disabled="hasEditPermission" :style="buttonStyle" @click="fileEdit(row)">修改</Button>
          <Button size="small" style="margin-right:5px;color:#3D73EF;border:1px solid #3D73EF;" @click="detailipfs(row)">详情</Button>
          <Button size="small" :disabled='hasEditPermission?hasEditPermission:row.uploadTime?true:false' :style="hasEditPermission?'color:#cccccc;border:1px solid #cccccc':row.uploadTime?'margin-right:5px;color:#cccccc;border:1px solid #cccccc': 'margin-right:5px;color:#3D73EF;border:1px solid #3D73EF'" @click="fileDelete(row)">删除</Button>
        </template>
      </Table>
      <Page :total="dataCount" :transfer='true' :page-size="tablePageParam.pageSize" show-sizer show-total show-elevator class="paging" @on-change="changepage" :current.sync="tablePageParam.pageIndex" style="text-align: right;margin-top:20px;" @on-page-size-change="pageSizeChange" :pageSizeOpts="pageSizeOpts"></Page>
    </div>

    <!-- 新建和修改弹框 -->
    <Modal v-model="addModal" :mask-closable="false" :title="modal1title" @on-cancel="closeAll">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
        <FormItem label="项目名称" prop="projectName">
          <Input v-model.trim="formValidate.projectName" show-word-limit maxlength="25" placeholder="请输入项目名称（注：最好与上传文档名一致）"></Input>
        </FormItem>
        <FormItem label="项目描述" prop="projectBrief">
          <Input v-model.trim="formValidate.projectBrief" show-word-limit maxlength="150" type="textarea" :autosize="{minRows: 3,maxRows: 5}" placeholder="请输入项目描述（注：与该项目上传文档相关的描述）"></Input>
        </FormItem>

      </Form>
      <div slot="footer">
        <Button @click="closeAll">取消</Button>
        <Button type="primary" @click="handleSubmit('formValidate')">确定</Button>
      </div>
    </Modal>
    <!-- 删除 -->
    <Modal v-model="modal3" width=25>
      <p style="text-align: center;height:30px;margin-top:20px; line-height: 30px;">请确认是否删除</p>
      <div slot="footer">
        <Button @click="closemodal3">取消</Button>
        <Button type="primary" @click="detailConfig">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { getFileList, getFileAdd, getFileListDetete, } from '@/api/contract'
import { localRead } from '@/lib/util'
export default {
  data () {
    return {
      deleId: '',
      pageSizeOpts: [10, 20, 40, 60, 100],
      userListData: [],
      status: '', // 搜索下拉框值
      dataCount: 0, // 总条数
      search_value: '', // 输入框
      tablePageParam: { pageIndex: 1, pageSize: 10 }, // 分页
      modal3: false,
      historyColumns: [//   table 表头

        {
          title: '项目名称',
          key: 'projectName',
        },
        {
          title: '项目说明',
          key: 'projectBrief',
        },

        {
          title: '创建时间',
          key: 'createTime',
        },
        {
          title: '上传时间',
          key: 'uploadTime',
        },

        {
          title: '操作',
          key: 'resultCode',
          slot: 'action',
          width: '190'
        }
      ],
      // 表格数据
      historyData: [],
      addModal: false,
      formValidate: {
        id: '',
        projectName: '',
        projectBrief: ''
      },
      ruleValidate: {// 字段规则
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        projectBrief: [
          { required: true, message: '请输入项目描述', trigger: 'blur' }
        ],

      },
      deleId: '',
      modal1title: '新建项目',
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  mounted () {
    this.getmanagementList()

  },
  computed: {


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },

    // 查询列表接口
    getmanagementList () {
      let params = {
        fileType: 1,
        projectName: this.search_value,
        pageParam: this.tablePageParam // 分页
      }
      getFileList(params).then((res) => {
        if (res.code === '00000') {
          // console.log(res)
          this.historyData = res.data.records
          this.tablePageParam = {
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.dataCount = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 打开新增配置弹窗
    addipfs () {
      this.modal1title = '新建项目'
      this.addModal = true
      this.$nextTick(() => {
        this.$refs.formValidate.resetFields()
      })
    },
    // 详情
    detailipfs (row) {
      this.$router.push({
        name: 'file_details',
        params: {
          id: row.id,
          projectName: row.projectName,
          projectBrief: row.projectBrief,
        }
      })
      sessionStorage.setItem('fileName', JSON.stringify(row))
      // let statusA = {
      //   '启动': 'ENABLE',
      //   '禁用': 'DISABLE'
      // }
      // this.isedit = true
      // this.formValidate = { ...row, status: statusA[row.status] }
      // this.addModal = true
    },
    // // 删除
    // remove (id) {
    //   this.modal3 = true
    //   this.deleId = id
    // },
    // 确定删除
    detailConfig (param) {
      let deteleId = {
        id: this.deleId
      }
      getFileListDetete(deteleId).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.modal3 = false
          this.getmanagementList()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 取消删除
    closemodal3 () {
      this.modal3 = false
    },
    // 翻页
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getmanagementList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getmanagementList()
    },
    // 重置按钮事件
    reset () {
      this.search_value = ''
      this.status = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getmanagementList()
    },
    // 表单提交
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.modal1title === '新建项目') {
            let addForm = {
              projectName: this.formValidate.projectName,
              projectBrief: this.formValidate.projectBrief,
            }
            getFileAdd(addForm).then((res) => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.addModal = false
                this.getmanagementList()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          } else {
            getFileAdd(this.formValidate).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.addModal = false
                this.getmanagementList()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          }
        }
      })
    },
    // 关闭所有弹窗清空所有值
    closeAll () {
      if (this.modal1title === '新建项目') {
        this.addModal = false
        this.$refs.formValidate.resetFields()
        this.formValidate.id = ''
      }
      this.$refs.formValidate.resetFields()
      this.addModal = false
    },
    // 编辑
    fileEdit (row) {
      this.addModal = true
      this.modal1title = '编辑项目'
      this.formValidate.projectName = row.projectName
      this.formValidate.projectBrief = row.projectBrief
      this.formValidate.id = row.id
    },
    //删除
    fileDelete (row) {
      this.modal3 = true
      this.deleId = row.id
    }
  },
  watch: {
  }

}
</script>

<style lang="less" scoped>
/deep/ .valueinput {
  textarea.ivu-input {
    padding-bottom: 15px;
    min-height: 80px !important;
    height: 120px !important;
    overflow-y: scroll !important;
  }
}
.cz_header {
  // display: flex;
  margin-top: 10px;
  /deep/ .ivu-select,
  /deep/ .ivu-date-picker {
    width: 15%;
    margin-right: 10px;
  }
}

//
/deep/ .ivu-modal {
  width: 700px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
//
.cz_gjz {
  height: 33px;
  line-height: 32px;
  width: 99px;
  text-align: center;
  border: 1px solid;
  background: #2d8cf0;
  color: #fff;
}
.ivu-btn-primary {
  margin-left: 7px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 50%;
}
/deep/ .ivu-table-wrapper {
  overflow: visible;
}

/deep/ .ivu-btn-small:hover {
  background-color: rgba(61, 115, 239, 0.8) !important;
  color: #fff !important;
}
/deep/ .ivu-btn-small:active {
  background-color: #3d73ef;
}

/deep/.ivu-tooltip-inner {
  max-width: 400px;
  z-index: 10000;
}
/deep/ .ivu-table-overflowX {
  overflow-x: hidden;
}
/deep/ .ivu-table:before {
  display: none;
}

// 弹窗样式
.input_reset_css {
  border: 1px solid #dcdee2;
  padding-bottom: 20px;
  border-radius: 4px;
  /deep/ .ivu-input {
    border: none !important;
  }
  /deep/ .ivu-input:focus {
    border: none !important;
    box-shadow: 0px 0px 0px #fff !important;
  }
}

.input_reset_css_focus {
  border-color: #57a3f3;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
/deep/ .ivu-form-item-error {
  .input_reset_css {
    border: 1px solid red;
  }
  // .input_reset_css{
  //     box-shadow:0 0 0 2px rgba(237,64,20,0.2);
  // }
  .input_reset_css_focus {
    //  border: 1px solid red;
    box-shadow: 0 0 0 2px rgba(237, 64, 20, 0.2);
  }
}
</style>
<style lang="less">
// .ivu-tooltip-popper{
//     max-height: 400px!important;
//     overflow-y:auto;
// }
.ivu-tooltip-contentr {
  max-height: 400px;
  overflow-y: auto;
}
.ivu-tooltip-inner {
  max-height: 300px;
  overflow-y: auto;
}
</style>
