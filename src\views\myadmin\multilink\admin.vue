<template>
  <div class="useradmin">
    <div class="top_header">
      <div class="search-controls">
        <Input placeholder="链名称" class="search-input" v-model="searchItem.chainName" />
        <Select v-model="searchItem.engineTypeList" multiple placeholder="选择链版本" class="search-select">
          <Option v-for="item in versionData" :value="item.type" :key="item.version">{{ item.type }}</Option>
        </Select>
        <Select v-model="searchItem.status" placeholder="选择运行状态" class="search-select">
          <Option v-for="item in statusList" :value="item.key" :key="item.value">{{ item.value }}</Option>
        </Select>
        <DatePicker type="daterange" v-model="dateRange" format="yyyy-MM-dd" placement="bottom-start" placeholder="选择创建时间范围" class="search-datepicker"></DatePicker>
        <Button type="primary" @click="searchList" icon="ios-search" class="search-btn">查询</Button>
        <Button type="primary" @click="reset" icon="md-sync" ghost class="search-btn">重置</Button>
      </div>
      <div class="action-buttons">
        <Button type="success" ghost @click="exportChain" icon="md-add" :disabled="hasEditPermission" class="action-btn">链数据导出</Button>
        <Button type="success" ghost @click="outChain" icon="md-add" :disabled="hasEditPermission" class="action-btn">链上数据导入</Button>
        <Button type="success" ghost @click="add" icon="md-add" :disabled="hasEditPermission" class="action-btn">纳管链</Button>
        <!-- <Button type="success" ghost @click="deployment" :disabled="hasEditPermission" icon="md-add" class="action-btn">一键部署</Button> -->
      </div>
    </div>
    <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10, 20, 40, 60, 100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align: right; margin: 10px 0" />
    <Modal :draggable="true" v-model="modal" width="700" :title="formItem.alertTitle" :z-index="1000" sticky :mask-closable="false">
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="150">
        <FormItem label="链名称：" prop="chainName">
          <Input placeholder="请输入" style="width: 500px; vertical-align: baseline" v-model="formItem.chainName" />
        </FormItem>
        <FormItem label="链版本：" prop="engineTypeKey">
          <Tooltip max-width="200" content="BOS：EOS升级版，PBFT共识交易更快，采用出块节点时区排序，有效降低网络延时。
EOS：DPOS BFT共识，按照出块节点账户名的字母排序出块，仅支持C++智能合约。
CMEOS：EOS移动增强版，支持C++和JavaScript智能合约。" style="margin-left: -18px">
            <Icon type="md-help-circle" style="font-size: 16px" />
          </Tooltip>
          <Select v-model="formItem.engineTypeKey" placeholder="选择链版本" style="width: 200px">
            <Option v-for="item in versionData" :value="item.type" :key="item.version">{{ item.type }}</Option>
          </Select>
        </FormItem>
        <FormItem label="开发架构链ID：" prop="eosChainId">
          <Input placeholder="请输入" :maxlength="64" show-word-limit style="width: 500px; vertical-align: baseline" v-model="formItem.eosChainId" />
        </FormItem>
        <FormItem label="合约升级：" prop="contractup">
          <Select v-model="formItem.contractup" style="width:200px" placeholder="请选择是否升级合约">
            <Option v-for="item in contractupList" :value="item.value" :key="item.value">{{ item.label }}</Option>

          </Select>
        </FormItem>
        <FormItem label="链所有权：" prop="ownership">
          <Select v-model="formItem.ownership" style="width: 200px">
            <Option value="MANAGED_CHAIN">纳管链</Option>
            <!-- <Option v-for="item in ownershipList" :value="item.value" :key="item.value">{{ item.value }}</Option> -->
          </Select>
        </FormItem>
        <FormItem label="主子链：" prop="subChain">
          <Select v-model="formItem.subChain" placeholder="选择主子链" style="width: 200px">
            <Option v-for="item in subChainList" :value="item.value" :key="item.value">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="链归属公司：" prop="company">
          <Select v-model="formItem.company" placeholder="请选择链归属公司" style="width: 200px" filterable>
            <Option v-for="item in selectList" :value="item.id" :key="item.id">{{item.companyName}}</Option>
          </Select>
        </FormItem>
        <FormItem label="历史数据导出方式：" prop="expType">
          <Select v-model="formItem.expType" placeholder="选择历史数据导出方式" style="width: 200px">
            <Option v-for="item in expTypeList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="审核列表：" prop="" class="mandatory">
          <Tooltip max-width="200" content="可选择租户需要人工审核的操作，为空系统自动审核" style="margin-left: -18px">
            <Icon type="md-help-circle" style="font-size: 16px" />
          </Tooltip>
          <Select v-model="formItem.needAudited" multiple placeholder="选择审核列表(多选)" style="width: 200px">
            <Option v-for="item in auditList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem v-if="formItem.status" label="状态：" prop="status">
          <Select v-model="formItem.status" placeholder="选择运行状态" style="width: 200px; margin: 0 5px">
            <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="主要业务描述：">
          <Input type="textarea" style="width: 500px" :maxlength="128" show-word-limit v-model="formItem.chainBrief" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写链的主要业务描述" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" ghost @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')">确定</Button>
      </div>
    </Modal>
    <!-- 链外数据导入 -->
    <Modal :draggable="false" v-model="modalimport" width="700" title="链上数据导入" :z-index="1000" sticky :mask-closable="false" @on-cancel="upCancel1">
      <p style="margin-bottom:2%;color:##dcdee2">请 <a @click="downloadTemplate" style="text-decoration: underline; color: #3d73ef; cursor: pointer;"> 点击此处</a>下载链上数据导入模板，填写完成后请在对应位置分别上传普通链账户、合约链账户、智能合约、普通链账户与合约链账户关联关系文件。</p>
      <!-- <p style="margin-bottom:2%;color:##dcdee2">请分别上传普通链账户、合约链账户、智能合约、普通链账户与合约链账户关联关系文件。</p> -->
      <Form :label-width="177">
        <FormItem label="普通链账户：" prop="" class="mandatoryl">
          <Upload action="" type="drag" multiple :accept="'.xls'" :format="['.xls']" :before-upload="handleUploadp" style="display:inline-table;width:82%;margin-top:10px;">
            <div v-if="!fileStatus.pstatus">
              <Icon type="ios-cloud-upload" size="40" style="color: #3399ff"></Icon>
              <p style="color: #aaa;font-size: 12px;">①点击上传普通链账户EXCLE文件</p>
            </div>
            <div v-else>
              <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff;" />
              <h4>完成文件上传</h4>
              <p style="color:#3399ff;font-size: 15px;">
                {{this.fileList.pFile?this.fileList.pFile.name:''}}
                <!-- <Icon type="ios-close" size="33"/> -->
              </p>
            </div>
          </Upload>
          <!-- <p>{{this.fileList.pFile?this.fileList.pFile.name:''}}</p> -->
        </FormItem>
        <FormItem label="合约链账户：" prop="" class="mandatoryl">
          <Upload action="" type="drag" multiple :accept="'.xls'" :format="['.xls']" :before-upload="handleUploady" style="display:inline-table;width:82%;margin-top:10px;">
            <div v-if="!fileStatus.hstatus">
              <Icon type="ios-cloud-upload" size="40" style="color: #3399ff"></Icon>
              <p style="color: #aaa;font-size: 12px;">②点击上传合约链账户EXCLE文件</p>
            </div>
            <div v-else>
              <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
              <h4>完成文件上传</h4>
              <p style="color: #3399ff;font-size: 15px;">{{this.fileList.hFile?this.fileList.hFile.name:''}}</p>
            </div>
          </Upload>
        </FormItem>
        <FormItem label="智能合约：" prop="" class="mandatoryl">
          <Upload action="" type="drag" multiple :accept="'.xls'" :format="['.xls']" :before-upload="handleUploadz" style="display:inline-table;width:82%;margin-top:10px;">
            <div v-if="!fileStatus.zstatus">
              <Icon type="ios-cloud-upload" size="40" style="color: #3399ff"></Icon>
              <p style="color: #aaa;font-size: 12px;">③点击上传智能合约EXCLE文件</p>
            </div>
            <div v-else>
              <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
              <h4>完成文件上传</h4>
              <p style=" color: #3399ff;font-size: 15px;">{{this.fileList.zFile?this.fileList.zFile.name:''}}</p>
            </div>
          </Upload>
        </FormItem>
        <FormItem label="链账户与合约关联关系：" prop="" class="mandatoryl">
          <Upload action="" type="drag" multiple :accept="'.xls'" :format="['.xls']" :before-upload="handleGuan" style="display:inline-table;width:82%;margin-top:10px;">
            <div v-if="!fileStatus.guanstatus">
              <Icon type="ios-cloud-upload" size="40" style="color: #3399ff"></Icon>
              <p style="color: #aaa;font-size: 12px;">④点击上传链账户与合约关联关系EXCLE文件</p>
            </div>
            <div v-else>
              <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
              <h4>完成文件上传</h4>
              <p style=" color: #3399ff;font-size: 15px;">{{this.fileList.guanFile?this.fileList.guanFile.name:''}}</p>
            </div>
          </Upload>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" ghost @click="upCancel">取消</Button>
        <Button type="primary" @click="upSumbit" :disabled="newdisabled" :loading="loadingStatus">{{ loadingStatus ? "上传中" : "确定" }}</Button>
      </div>
    </Modal>
    <!-- 取消 -->
    <Modal :draggable="true" v-model="closeModal" width='500' class="quxiao" title="" :z-index="1000" sticky :mask-closable="false" :closable="false">
      <p style="color:#888A8C;font-size: 20px;text-align: center;margin-top: 10%;">取消后不保留当前上传文件</p>
      <div slot="footer" style="text-align:center;margin-top: 2%;">
        <Button type="primary" ghost @click="closeCancel">取消</Button>
        <Button type="primary" @click="okCancle">确定</Button>
      </div>
    </Modal>
    <!-- 上传成功/失败 -->
    <Modal :draggable="true" v-model="continueModal" width="550" title="" :z-index="1000" sticky :mask-closable="false" :closable="false" :footer-hide='true'>
      <!-- 成功 -->
      <div v-if="showhide">
        <p style="text-align:center;">
          <Icon type="md-checkmark-circle" size="60" style="color:#1296DB" />
        </p>
        <p style="color:#888A8C;font-size: 20px;text-align: center;margin-top: 5%;">文件上传成功</p>
        <div slot="footer" style="text-align: center;margin-top: 8%;">
          <Button type="primary" ghost @click="successCancel" style="width: 80px;">取消</Button>
          <Button type="primary" style="margin-left: 15%;" @click="continueUp">继续上传文件</Button>
        </div>
      </div>
      <!-- 失败 -->
      <div v-else>
        <p style="text-align:center;">
          <Icon type="md-close-circle" size="50" style="text-align: center;color:red" />
        </p>
        <p style="color:#888A8C;font-size: 20px;text-align: center;margin-bottom:2%">文件上传失败</p>
        <ul class="choose" v-if="shibai">
          <li v-for="item in failResult" :key="item.failRow">
            <p>（{{item.indexkey}}）错误位置：{{item.failRow}}行 &nbsp;&nbsp;{{item.failName}}列：</p>
            <p style="margin-left: 16.5%;">错误原因：{{item.failReason}}</p>
          </li>
        </ul>
        <p v-else style="color:#3399ff;font-size: 15px;text-align:center">{{shibaiinfo}}</p>
        <div slot="footer" style="text-align: center;margin-top: 5%;">
          <Button type="primary" @click="continueUp">确定</Button>
        </div>
      </div>
    </Modal>
    <!-- 操作日志 -->
    <Modal v-model="modallog" title="操作日志" footer-hide>
      <p style="margin-bottom: 1%;">一键部署</p>
      <div class="execute">
        <p v-for="item in oneDeployment" :key="item.chainId">{{item.createTime}}&nbsp;:&nbsp;{{item.message}}</p>
      </div>
      <p style="padding: 2% 2% 2% 0;">初始化</p>
      <div class="execute">
        <p v-for="item in cmesoInitStatus" :key="item.chainId">{{item.createTime}}&nbsp;:&nbsp;{{item.message}}</p>
      </div>
      <p style="padding: 2% 2% 2% 0;">节点操作日志</p>
      <div class="execute">
        <p v-for="item in cmeosnode" :key="item.chainId">{{item.createTime}}&nbsp;:&nbsp;{{item.message}}</p>
      </div>

    </Modal>
    <deplooyModal ref="child" />
  </div>
</template>
<script>
import { tenantcompanyList, uploadimportMainChain, getconfig, getExportList } from '@/api/contract'
import { getChainIdListv2, reviseMultiChain, addMultiChain } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { searchKey } from './tool'
import { engineTypeList, statusList, ownershipList, expTypeList, deplooyList } from './typeList'
import deplooyModal from './deplooyModal'
import { getLogList } from '@/api/arrange'
import { localRead } from '@/lib/util'
export default {
  name: 'multilink_admin',
  components: {
    EditTableMul,
    deplooyModal
  },
  data () {
    // const validateArr = (rule, value, callback) => {
    //   if (value.length === 0) {
    //     callback(new Error('请至少选择一项'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      intervalId: null,
      modallog: false,
      cmeosnode: [],
      cmesoInitStatus: [],
      oneDeployment: [],
      // 以上是一键部署新加的
      shibaiinfo: '',
      shibai: false,
      loadingStatus: false,
      closeModal: false,
      failResult: [],
      showhide: false,
      modalimport: false, // 链外数据导入
      continueModal: false, // 文件成功
      fileStatus: { pstatus: false, hstatus: false, zstatus: false, guanstatus: false },
      fileList: { pFile: null, hFile: null, zFile: null, guanFile: null },
      // 以上是主链纳管新加的
      maxsize: (() => {
        try {
          return localStorage.getItem('MAX_FILE_SIZE') ? JSON.parse(localStorage.getItem('MAX_FILE_SIZE')) : 2048;
        } catch (e) {
          console.error('localStorage MAX_FILE_SIZE JSON解析错误:', e);
          return 2048;
        }
      })(),
      subtrue: false,
      selectList: [], // 归属公司
      subChainList: [{ value: 'MAIN_CHAIN', name: '集团主链' }, { value: 'SUB_CHAIN', name: '省子链' }], // 主子链
      expTypeList: [],
      modal: false,
      transferKey: 0,
      engineTypeList: [],
      statusList: [],
      auditList: [],
      ownershipList: [],
      contractupList: [{
        value: '1',
        label: '是'
      },
      {
        value: '0',
        label: '否'
      }],
      searchItem: {
        chainName: '',
        engineTypeList: [],
        status: ''
      },
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      formItem: {
        alertTitle: '新增纳管链',
        chainId: '',
        chainName: '',
        eosChainId: '',
        chainBrief: '',
        ownership: '',
        needAudited: [],
        status: '',
        engineTypeKey: '',
        ownershipKey: '',
        needAuditedKey: '',
        statusKey: '',
        expType: '',
        subChain: '',
        company: '',
        contractup: '',
        chainVersion: '' // 链版本
      },
      formItemRule: {
        chainName: [{ required: true, message: '不能为空', trigger: 'blur' }, { max: 16, message: '不能多于16位', trigger: 'blur' }],
        eosChainId: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[a-z0-9]{64}$/,
            message: '格式有误,长度必须为64位,a-z0-9',
            trigger: 'blur'
          }
        ],
        engineTypeKey: [
          { required: true, message: '请选择链版本', trigger: 'change' }
        ],
        ownership: [
          { required: true, message: '请选择一项', trigger: 'change' }
        ],
        chainBrief: [
          {
            required: true,
            message: '请填写链的主要业务描述',
            trigger: 'blur'
          }
        ],
        company: [{ required: true, message: '请选择链归属公司', trigger: 'change', type: 'number' }],
        status: [{ required: true, message: '请选择一项', trigger: 'change' }],
        expType: [{ required: true, message: '请选择一项', trigger: 'change' }],
        subChain: [
          { required: true, message: '请选择主子链', trigger: 'change' }
        ],
        contractup: [{ required: true, message: '请选择是否升级合约', trigger: 'change' }]
      },
      columns: [
        { key: 'chainName', title: '链名称' },
        // { key: 'chainBrief', title: '描述', tooltip: true },
        {
          key: 'engineTypeKey',
          title: '链版本',
          render: (h, params) => {
            const engineType = params.row.engineTypeKey || '';
            const version = params.row.chainVersion || '';
            const displayText = engineType && version ? `${engineType} ${version}` : engineType || version || '';
            return h('span', displayText);
          }
        },
        // { key: 'eosChainId', title: '开发架构链ID' },
        { key: 'isUpgradeContract', title: '合约升级' },
        { key: 'ownership', title: '所有权' },
        {
          key: 'status',
          title: '状态',
          render: (h, params) => {
            const color =
              params.row.statusKey === 'ENABLE' ? '#15AD31' : '#C7C7C7'
            return h(
              'Tag',
              {
                props: {
                  type: 'dot',
                  color: color
                },
                style: { marginLeft: '-8px' }
              },
              params.row.status
            )
          }
        },
        { key: 'needAudited', title: '审核列表' },
        { key: 'expType', title: '导出方式' },
        { key: 'createTime', title: '创建时间' },
        {
          key: 'action',
          title: '操作',
          width: 190,
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                  style: this.buttonStyle,
                  on: {
                    click: () => {
                      this.editTenant(params.index)
                    }
                  }
                },
                '编辑'
              ),
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small' },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.showDetails(params.index)
                    }
                  }
                },
                '详情'
              ),
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small' },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF',
                    display: params.row.ownership == '管控链' ? '' : 'none'
                  },
                  on: {
                    click: () => {
                      this.clicklog(params.row)
                    }
                  }
                },
                '日志'
              )
            ])
          }
        }
      ],
      tableData: [],
      versionData: [],
      dateRange: [],
      userPermission: (() => {
        try {
          return JSON.parse(localRead('userPermission'));
        } catch (e) {
          console.error('userPermission JSON解析错误:', e);
          return {};
        }
      })(),
    }
  },
  methods: {
    // 格式化开始日期时间为 yyyy-MM-dd 00:00:00 格式
    formatDateTimeStart (date) {
      if (!date) return null;
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day} 00:00:00`;
    },
    // 格式化结束日期时间为 yyyy-MM-dd 23:59:59 格式
    formatDateTimeEnd (date) {
      if (!date) return null;
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day} 23:59:59`;
    },
    // 链数据导出
    exportChain () {
      let data = {
        "chainName": this.searchItem.chainName,
        "engineTypeList": this.searchItem.engineTypeList,
        "statusKey": this.searchItem.status,
        "pageParam": { "pagetotal": 31, "pageSize": 10, "pageIndex": 1 }
      }
      getExportList(data).then(blob => {
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = '全量链数据导出' + '.xlsx'
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        if (error instanceof Blob) {
          error.text().then(text => {
            let msg = JSON.parse(text)
            this.msgInfo('error', msg.message, true)
          })

        }


      })
    },
    // 日志
    clicklog (row) {
      // console.log(this.tableData[index])
      this.modallog = true
      getLogList(row.chainId).then((res) => {
        this.cmesoInitStatus = res.data.cmesoInitStatus
        this.oneDeployment = res.data.oneDeployment
        this.cmeosnode = res.data.cmeosnode
      })
    },
    // 一键部署
    deployment () {
      this.$refs.child.clickmodal('一键部署')
    },
    downloadTemplate () {
      window.location.href = '/链上数据导入模板.zip'
    },
    // 链外数据导入
    outChain () {
      this.modalimport = true
    },
    // 普通链账户
    handleUploadp (file) {
      if (file.name === '普通链账户.xls') {
        this.fileList.pFile = file
        this.fileStatus.pstatus = true
      } else {
        this.msgInfo('error', '请上传命名格式为“普通链账户.xls”的表格文件', true)
      }
    },
    // 合约链账户
    handleUploady (file) {
      if (file.name === '合约链账户.xls') {
        this.fileList.hFile = file
        this.fileStatus.hstatus = true
      } else {
        this.msgInfo('error', '请上传命名格式为“合约链账户.xls”的表格文件', true)
      }
    },
    // 智能合约
    handleUploadz (file) {
      if (file.name === '智能合约.xls') {
        this.fileList.zFile = file
        this.fileStatus.zstatus = true
      } else {
        this.msgInfo('error', '请上传命名格式为“智能合约.xls”的表格文件', true)
      }
    },
    // 关联关系
    handleGuan (file) {
      if (file.name === '链账户与合约关联关系.xls') {
        this.fileList.guanFile = file
        this.fileStatus.guanstatus = true
      } else {
        this.msgInfo('error', '请上传命名格式为“链账户与合约关联关系.xls”的表格文件', true)
      }
    },
    initimport () {
      this.fileList = { pFile: null, hFile: null, zFile: null, guanFile: null }
      this.fileStatus = { pstatus: false, hstatus: false, zstatus: false, guanstatus: false }
    },
    upCancel1 () {
      this.initimport()
    },
    upCancel () {
      this.closeModal = true
    },
    // 上传成功取消
    successCancel () {
      this.continueModal = false
    },
    // 继续上传文件
    continueUp () {
      this.modalimport = true
      this.continueModal = false
    },
    // 确认取消
    okCancle () {
      this.initimport()
      this.closeModal = false
      this.modalimport = false
    },
    closeCancel () {
      this.closeModal = false
    },
    // 提交文件
    upSumbit () {
      this.loadingStatus = true
      uploadimportMainChain(this.fileList).then(res => {
        if (res.code !== '00000') {
          this.msgInfo('error', res.message, true)
          this.loadingStatus = false
          return
        }
        if (res.data.status === 'success') {
          this.continueModal = true
          this.showhide = true
          this.initimport()
          this.modalimport = false
          this.loadingStatus = false
        } else {
          if (res.data.message === null) {
            this.modalimport = false
            this.continueModal = true
            this.showhide = false
            this.loadingStatus = false
            this.shibai = true
            // this.initimport()
            res.data.failResult.forEach((item, index) => {
              item.indexkey = index + 1
            })
            this.failResult = res.data.failResult
          } else {
            this.modalimport = false
            this.continueModal = true
            this.showhide = false
            this.loadingStatus = false
            this.shibai = false
            this.shibaiinfo = res.data.message
          }
        }
      }).catch(error => {
        this.loadingStatus = false
        this.msgInfo('error', error.message, true)
      })
      // }
    },

    // onSelect (value) {
    //   if (value.value === 'SUB_CHAIN') {
    //     this.subtrue = true
    //   } else if (value.value === 'MAIN_CHAIN') {
    //     this.subtrue = false
    //     this.formItem.company = ''
    //   }
    // },
    // 归属公司
    attribution () {
      let listcompany = {
        companyName: ''
      }
      tenantcompanyList(listcompany).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        alertTitle: '新增纳管链',
        chainName: '',
        eosChainId: '',
        chainBrief: '',
        engineTypeKey: '',
        ownership: '',
        needAudited: [],
        status: '',
        expType: '',
        subChain: '',
        company: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getTableData()
        this.modal = false
      } else {
        // console.log('tipInfo-error:', res.message)
        this.msgInfo('error', res.message, true)
      }
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    getTableData () {
      this.searchItem.startTime = this.dateRange && this.dateRange.length > 0 ? this.formatDateTimeStart(this.dateRange[0]) : null,
        this.searchItem.endTime = this.dateRange && this.dateRange.length > 0 ? this.formatDateTimeEnd(this.dateRange[1]) : null,
        getChainIdListv2(this.tablePageParam, this.searchItem)
          .then((res) => {
            // this.tableData = res.data.records
            if (res.code === '00000') {
              let typedata = {
                0: '不导出',
                1: 'hyperion导出',
                2: '数据管理组件导出'

              }
              let Contract = {
                '1': '是',
                '0': '否'
              }
              let data = res.data.records.map((item) => {
                if (item.auditList) {
                  item['needAudited'] =
                    item.auditList.map((val) => val.auditValue).join(',') || ''
                } else {
                  item.needAudited = item.needAudited ? '需要' : '不需要'
                }
                return {
                  ...item,
                  expType: typedata[item.expType],
                  isUpgradeContract: Contract[item.isUpgradeContract]

                }
              })
              this.tableData = data
              // this.tableData = this.changeNeedAudited(res.data.records)
              this.tablePageParam = {
                pagetotal: res.data.total,
                pageSize: res.data.size,
                pageIndex: res.data.current
              }
              ++this.transferKey
            } else {
              this.msgInfo('error', res.message, true)
            }
          })
          .catch((error) => {
            this.msgInfo('error', error.message, true)
          })
    },
    searchList () {
      // this.tablePageParam.pageIndex = 1
      this.getTableData()
    },
    // changeNeedAudited (list) {
    //   for (var i = 0; i < list.length; i++) {
    //     if (list[i].auditList) {
    //       list[i]['needAudited'] =
    //         list[i].auditList.map((val) => val.auditValue).join(',') || ''
    //     } else {
    //       list[i].needAudited = list[i].needAudited ? '需要' : '不需要'
    //     }
    //     // if (list[i].needAudited) {
    //     //   list[i].needAudited = '需要'
    //     // } else {
    //     //   list[i].needAudited = '不需要'
    //     // }
    //     // list[i].needAudited = arrToDic(this.auditList)[list[i].needAudited]
    //   }
    //   return list
    // },
    reset () {
      this.searchItem = {
        chainName: '',
        engineTypeList: [],
        status: ''
      }
      this.dateRange = []
      this.searchList()
    },

    ok (name) {
      //   console.log(this.formItem)
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.formItem.subChain === 'SUB_CHAIN' && this.formItem.company === '') {
            this.msgInfo('error', '请选择链归属公司', true)
          } else {
            const input = this.formItem.engineTypeKey;
            const [param1, param2] = input.split(" "); // 按空格分割
            // this.formItem.ownershipKey = searchKey(
            //   this.formItem.ownership,
            //   this.ownershipList
            // )
            //   this.formItem.expType = searchKey(
            //     this.formItem.expType,
            //     this.expTypeList
            //   )
            // this.formItem.needAuditedKey = searchKey(this.formItem.needAudited, this.auditLists)
            this.formItem.auditList = this.formItem.needAudited || []
            // delete this.formItem.needAudited
            // console.log(this.formItem, 'rrr')
            // console.log('this.formItem.status:', this.formItem.status)
            if (this.formItem.status) {
              this.formItem.statusKey = searchKey(
                this.formItem.status,
                this.statusList
              )

              // console.log(this.formItem, '111111111111', searchKey(this.formItem.expType, this.expTypeList))
              let item = {
                // alertTitle: this.formItem.alertTitle,
                auditList: this.formItem.auditList,
                chainBrief: this.formItem.chainBrief,
                chainId: this.formItem.chainId,
                chainName: this.formItem.chainName,
                engineTypeKey: param1,
                chainVersion: param2,
                eosChainId: this.formItem.eosChainId,
                expType: searchKey(this.formItem.expType, this.expTypeList),
                // expType: 1,
                // needAudited: this.formItem.formItem,
                // ownership: this.formItem.ownership,
                ownershipKey: 'MANAGED_CHAIN', // this.formItem.ownershipKey,
                // status: this.formItem.status,
                statusKey: this.formItem.statusKey,
                chainSource: this.formItem.subChain, // 主子链
                companyId: this.formItem.company, // 公司id
                isUpgradeContract: this.formItem.contractup
              }
              reviseMultiChain(item).then((res) => {
                this.tipInfo(res)
                this.init()
              }).catch((error) => {
                // console.log('revise-error:', error)
                this.msgInfo('error', error.message, true)
              })
            } else {
              //    console.log(this.formItem,'w2222222222')
              let item1 = {
                auditList: this.formItem.auditList,
                chainBrief: this.formItem.chainBrief,
                chainName: this.formItem.chainName,
                engineTypeKey: param1,
                chainVersion: param2,
                eosChainId: this.formItem.eosChainId,
                expType: searchKey(this.formItem.expType, this.expTypeList),
                // expType: 1,
                ownershipKey: 'MANAGED_CHAIN', // this.formItem.ownershipKey,
                chainSource: this.formItem.subChain, // 主子链
                companyId: this.formItem.company, // 公司id
                isUpgradeContract: this.formItem.contractup
                // status: this.formItem.status,
                // needAudited: this.formItem.needAudited,
                // ownership: this.formItem.ownership,
                // alertTitle: this.formItem.alertTitle,
              }
              addMultiChain(item1).then((res) => {
                this.tipInfo(res)
                // this.init()
              }).catch((error) => {
                // console.log('add-error:', error)
                this.msgInfo('error', error.message, true)
              })
            }
          }
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
      // this.$refs[name].resetFields()
      // this.init()
    },
    cancel (name) {
      this.init()
      this.modal = false
      this.subtrue = false
    },
    add (name) {
      this.init()
      this.modal = true
      this.subtrue = false
      this.attribution()
    },
    showDetails (index) {
      this.$router.push({
        name: 'multilink_details',
        params: {
          chainId: `${this.tableData[index].chainId}`,
          eosChainId: `${this.tableData[index].eosChainId}`,
          chainSource: `${this.tableData[index].chainSource}`,
          companyName: `${this.tableData[index].companyName}`,
          ownership: `${this.tableData[index].ownership}`
        }
      })
    },
    editTenant (index) {
      if (this.tableData[index].ownership === '管控链') {
        this.$refs.child.clickmodal('修改管控链', this.tableData[index])
      } else {
        this.attribution()
        this.init()
        this.modal = true

        // if (this.tableData[index].chainSource === 'MAIN_CHAIN') {
        //   this.subtrue = false
        // } else {
        //   this.subtrue = true
        // }
        // let exptype = ''
        // if (`${this.tableData[index].chainName}`) {
        // console.log('CMEOS V3.0.9' === `${this.tableData[index].engineTypeKey} ${this.tableData[index].chainVersion}`);

        this.formItem = {
          alertTitle: '修改纳管链',
          chainId: `${this.tableData[index].chainId}`,
          chainBrief: `${this.tableData[index].chainBrief}`,
          chainName: `${this.tableData[index].chainName}`,
          engineTypeKey: this.tableData[index].engineTypeKey && this.tableData[index].chainVersion
            ? `${this.tableData[index].engineTypeKey} ${this.tableData[index].chainVersion}`
            : '',
          eosChainId: `${this.tableData[index].eosChainId}`,
          ownership: 'MANAGED_CHAIN', // `${this.tableData[index].ownership}`,
          needAudited: this.tableData[index].auditList.map((val) => val.auditKey),
          status: `${this.tableData[index].status}`,
          expType: `${this.tableData[index].expType}`,
          subChain: `${this.tableData[index].chainSource}`,
          company: this.tableData[index].companyId,
          contractup: `${this.tableData[index].isUpgradeContract}` === '是' ? '1' : '0'
        }
        this.subtrue = this.tableData[index].chainSource !== 'MAIN_CHAIN'
        // console.log(this.formItem, 'this.formItem')
        // }
      }
    }
  },
  computed: {
    auditLists () {
      return this.$store.state.dict.options.AUDIT_TYPE || []
    },
    newdisabled: function () {
      if (this.fileList.pFile == null || this.fileList.hFile == null || this.fileList.zFile == null || this.fileList.guanFile == null) {
        return true
      } else {
        return false
      }
      // return this.formItem.contractName === '' || this.formItem.VmType === ''
    },



    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin || this.userPermission.isTenantAdmin || this.userPermission.isTenant
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },

  },
  // watch: {
  //   tableData: {
  //     handler (newVal) {
  //       //
  //     },
  //     deep: true,
  //     immediate: false
  //   }
  // },
  created () {
    this.deplooyList = deplooyList
    this.engineTypeList = engineTypeList
    this.statusList = statusList
    // this.auditList = auditList
    this.ownershipList = ownershipList
    this.expTypeList = expTypeList
    // getListTypeMap('AUDIT_TYPE').then(res => {
    //   this.auditList = optionList(res.data)
    // })
    // this.intervalId = setInterval(() => {
    //   this.getTableData()// 每两秒发送一次请求
    // }, 5000)
  },
  mounted () {
    this.getTableData()

    this.$store
      .dispatch('getOptions', 'AUDIT_TYPE')
      .then((result) => {
        if (Object.prototype.toString.call(result) === '[object Boolean]') {
          this.auditList = this.auditLists
          // console.log(this.auditList)
        } else {
          this.auditList = result
        }
      })
      .catch((err) => {
        // console.log(err, 'err')
        this.msgInfo('error', err.message, true)
      })
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })

    // 获取版本号和说明
    getconfig('CHAIN_VERSION_CONFIG').then((res) => {
      if (res.data) {
        if (res.data.value) {
          try {
            console.log('原始数据:', res.data.value);
            this.versionData = JSON.parse(res.data.value);
            console.log('解析后的数据:', this.versionData);
          } catch (jsonError) {
            console.error('JSON解析错误:', jsonError);
            console.error('原始数据:', res.data.value);
            this.msgInfo('error', 'JSON数据格式错误: ' + jsonError.message, true);
            // 设置默认值
            this.versionData = [];
          }
        }
      } else {
        console.log(res)
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
  },
  deactivated () {
    this.modal = false
  }
}
</script>
<style lang="less" scoped>
.execute {
  background: #f2f2f2;
  height: 80px;
  padding-top: 2%;
  padding-bottom: 2%;
  padding-left: 1%;
  overflow-y: auto;
}
.top_header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin: 10px 10px 15px 0px;

  .search-controls {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    flex: 1;
    min-width: 0;

    .search-input {
      width: 180px;
      min-width: 120px;
      flex-shrink: 0;
    }

    .search-select {
      width: 170px;
      min-width: 120px;
      flex-shrink: 0;
    }

    .search-datepicker {
      width: 220px;
      min-width: 180px;
      flex-shrink: 0;
    }

    .search-btn {
      flex-shrink: 0;
    }
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;

    .action-btn {
      flex-shrink: 0;
      white-space: nowrap;
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .top_header {
    .search-controls {
      .search-input,
      .search-select {
        width: 150px;
        min-width: 100px;
      }

      .search-datepicker {
        width: 200px;
        min-width: 160px;
      }
    }
  }
}

@media (max-width: 992px) {
  .top_header {
    flex-direction: column;
    align-items: stretch;

    .search-controls {
      justify-content: flex-start;
      margin-bottom: 10px;
    }

    .action-buttons {
      justify-content: flex-start;
    }
  }
}

@media (max-width: 768px) {
  .top_header {
    .search-controls {
      .search-input,
      .search-select {
        width: 130px;
        min-width: 100px;
      }

      .search-datepicker {
        width: 180px;
        min-width: 140px;
      }
    }
  }
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  // background-color: red;
  background-color: rgb(135, 158, 235);
}
.choose {
  margin-top: 2%;
  display: flex;
  width: 90%;
  height: 200px;
  overflow-y: scroll;
  margin: 0 auto;
  border-radius: 10px;
  // display: flex;
  flex-wrap: wrap;
  background: #f5f5ff;
  border: 1px solid #d9d9da;
  li {
    width: 100%;
    height: 60px;
    margin-top: 3%;
    p {
      margin-left: 9%;
      // text-align: center;
      color: #3399ff;
      font-size: 15px;
    }
  }
}
.mandatoryl {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: " ";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    // font-family: SimSun;
    // font-size: 14px;
    // color: #ed4014;
  }
}
span.search-title {
  font-size: 12px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
button.btn1 {
  position: absolute;
  right: 12%;
  margin: 0 10px;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
.quxiao {
  /deep/.ivu-modal-content {
    height: 220px;
  }
  /deep/.ivu-modal-footer {
    border: none;
  }
}
</style>
