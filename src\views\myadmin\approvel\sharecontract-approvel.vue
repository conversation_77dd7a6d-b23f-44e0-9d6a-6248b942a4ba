<template>
  <div class="contractApp">
    <Tabs :value="name" @on-click="clickTab">
      <TabPane label="待审批" name="name1">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入合约名称或应用名称" style="vertical-align:baseline;" v-model="queryContractName" />
          <Select class='bt1 width-input' v-model="userId" placeholder="请选择发布租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
        </p>
        <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
      </TabPane>
      <TabPane label="已审批" name="name2">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入合约名称或应用名称" style="vertical-align:baseline;" v-model="queryContractName2" />
          <Select class='bt1 width-input' v-model="userId2" placeholder="请选择发布租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Select class='bt1 width-input' v-model="status2" placeholder="请选择状态" multiple :max-tag-count="2">
            <Option v-for="item in options" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList2">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting2">重置</Button>
        </p>
        <edit-table-mul :columns="columns2" v-model="tableData2"></edit-table-mul>
        <Page :total="tablePageParam2.pagetotal" :current.sync="tablePageParam2.pageIndex" @on-change="pageChange2" :page-size="tablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;line-height:40px" />
      </TabPane>
    </Tabs>
    <Modal v-model="modalAppro" :title="appModal" width="900" :draggable="true" :height="getHeight" sticky :mask-closable="false">
      <Card dis-hover>
        <p class="title bs"> 合约信息 </p>
        <span>合约名称：{{ arrDetails.contractName }}</span><br>
        <span>合约语言：{{ this.languageType==='JS'?'Java Script':this.languageType  }}</span><br>
        <span>应用名称：{{ arrDetails.contractReadableName }}</span><br>
        <p style="word-break:break-all;white-space: pre-wrap;margin-left:42px;text-indent: -42px;">应用简介：{{ arrDetails.brief }}</p>
        <span>合约版本：{{ arrDetails.uploadVersion }}</span><br>
        <span>合约链账户：{{ arrDetails.chainAccountName }}</span><br>
      </Card>
      <Card style="margin-top:5px;">
        <p class="title bs">共享租户</p>
        <edit-table-mul :columns="tenantColumns" v-model="shareTenantList"></edit-table-mul>
        <!-- <Page :total="tenantTablePageParam1.pagetotal" :current.sync="tenantTablePageParam1.pageIndex" @on-change="pageChange" :page-size="tenantTablePageParam1.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px"/> -->
      </Card>
      <div slot="footer">
        <Card dis-hover>
          <p class="title" style="text-align:left;">审批意见</p>
          <i-Form :model="formItem" :rules="formItemRule" :label-width="80" ref="formItem" style="text-align:left;">
            <FormItem style="margin:1px;padding:1px;" label="是否同意" prop="approStatus">
              <RadioGroup v-model="formItem.approStatus">
                <Radio label="APPROVED">同意</Radio>
                <Radio label="REJECT">不同意</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" prop="auditRemark" v-show="formItem.approStatus==='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" v-show="formItem.approStatus!=='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）" :maxlength="30" show-word-limit></Input>
            </FormItem>
          </i-Form>
          <Button type="primary" @click="ok('formItem')" :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交" }}</Button>
          <Button type="default" @click="cancelApp('formItem')">取消</Button>
        </Card>
      </div>
    </Modal>
    <Modal v-model="modalDetail" :title="appModal" width="900" :draggable="true" :mask-closable="false" sticky>
      <Card dis-hover>
        <p class="title bs"> 合约信息 </p>
        <span>合约名称：{{ arrDetails.contractName }}</span><br>
        <span>合约语言： {{ this.languageType==='JS'?'Java Script':this.languageType }}</span><br>
        <span>应用名称：{{ arrDetails.contractReadableName }}</span><br>
        <p style="word-break:break-all;white-space: pre-wrap;margin-left:42px;text-indent: -42px;">应用简介：{{ arrDetails.brief }}</p>
        <span>合约版本：{{ arrDetails.uploadVersion }}</span><br>
        <span>合约链账户：{{ arrDetails.chainAccountName }}</span><br>
      </Card>
      <Card style="margin-top:5px;" dis-hover>
        <p class="title bs">共享租户</p>
        <edit-table-mul :columns="tenantColumns" v-model="shareTenantList"></edit-table-mul>
        <!-- <Page :total="tenantTablePageParam2.pagetotal" :current.sync="tenantTablePageParam2.pageIndex" @on-change="pageChange" :page-size="tenantTablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px"/> -->
      </Card>
      <br>
      <div slot="footer" class="bg1" :bordered="false" v-show="arrDetails.lastAuditStatus === 'APPROVED'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left; margin-left:5px; margin-top:15px;">结果:<span class="resultS">{{ arrDetails.lastAuditStatusDesc }}</span></p>
          <p style="text-align:left; margin-left:5px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
      <div slot="footer" class="bg2" :bordered="false" v-show="arrDetails.lastAuditStatus !== 'APPROVED'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left;margin-left:5px;margin-top:10px;">结果:<span class="resultF">{{ arrDetails.lastAuditStatusDesc }}</span></p>
          <p style="text-align:left;margin-left:5px;margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getUserData, contractApprovel, shareContractData, shareContractDetail } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { transferToStr } from './util.js'

export default {
  name: 'sharecontract_approvel',
  components: {
    EditTableMul
  },
  data () {
    return {
      loadingStatus: false,
      name: this.$route.params.tabs || 'name1',
      transferKey1: 0,
      transferKey2: 0,
      modalAppro: false,
      modalDetail: false,
      formItem: {
        auditRemark: '',
        approStatus: ''
      },
      formItemRule: {
        approStatus: [{ required: true, message: '请选择是否同意！', trigger: 'change' }],
        auditRemark: [{ required: true, message: '请填写审批说明！', trigger: 'blur' }]
      },
      bizId: '',
      appModal: '',
      approStatus: '',
      contype: '',
      queryContractName: '',
      queryContractName2: '',
      userIdStr: '',
      status2: [],
      statusStr: '',
      statusStr2: '',
      userId: [],
      userId2: [],
      userListData: [],
      options: [{
        value: 'APPROVED',
        label: '共享审核通过'
      }, {
        value: 'REJECT',
        label: '审核不通过'
      }, {
        value: 'REMOVED',
        label: '已下架'
      }
      ],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      tablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      tenantTablePageParam1: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      tenantTablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'readableName', title: '应用名称', tooltip: true },
        { key: 'chainAccountName', title: '链账户', tooltip: true },
        { key: 'submitName', title: '平台账号', tooltip: true },
        { key: 'tenantName', title: '发布租户', tooltip: true },
        { key: 'chainName', title: '链名称', tooltip: true },
        { key: 'submitTime', title: '提交时间', width: '170px', tooltip: true },
        // { key: 'auditName',
        //   title: '审批人',
        //   width: '80px',
        //   tooltip: true,
        //   render: (h, params) => {
        //     return h('span', params.row.auditName === null ? '/' : params.row.auditName)
        //   }
        // },
        {
          key: 'statusMsg',
          title: '状态',
          minWidth: 130,
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.statusCode === 'UNAPPROVED' ? 'primary' : '#515a6e'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.statusMsg)
          }
        },
        {
          key: 'action',
          title: '操作',
          align: 'left',
          width: 100,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '审批')

            ])
          }
        }
      ],
      arrDetails: {},
      columns2: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'readableName', title: '应用名称', minWidth: 10, tooltip: true },
        { key: 'chainAccountName', title: '链账户', tooltip: true },
        { key: 'submitName', title: '平台账号', tooltip: true },
        { key: 'tenantName', title: '发布租户', minWidth: 1, tooltip: true },
        { key: 'chainName', title: '链名称', tooltip: true },
        { key: 'submitTime', title: '提交时间', width: '170px', tooltip: true },
        { key: 'auditName', title: '审批人', width: '80px', tooltip: true },
        {
          key: 'statusMsg',
          title: '状态',
          minWidth: 120,
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.statusCode === 'APPROVED' ? 'primary' : '#515a6e'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.statusMsg)
          }
        },
        {
          key: 'action',
          title: '操作',
          width: 100,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.ShowDetails(params.index)
                  }
                }
              }, '详情')
            ])
          }
        }
      ],
      tableData: [],
      tableData2: [],
      shareTenantList: [],
      tenantColumns: [
        { key: 'tenantName', title: '租户名称', tooltip: true, minWidth: 20 },
        { key: 'tenantBrief', title: '租户描述', tooltip: true }
      ],
      shareTenantData1: [],
      shareTenantData2: [],
      tenantColumns1: [
        { key: 'tenantName', title: '租户名称', tooltip: true, minWidth: 20 },
        { key: 'tenantBrief', title: '租户描述', tooltip: true }
      ],
      tenantColumns2: [
        { key: 'tenantName', title: '租户名称', tooltip: true, minWidth: 20 },
        { key: 'tenantBrief', title: '租户描述', tooltip: true }
      ],
      languageType: ''
    }
  },
  computed: {
    getHeight: function (value) {
      if (this.tableData.length === 0) {
        return 90
      } else if (this.tableData.length < 5) {
        return 90 + 48 * (this.tableData.length - 1)
      } else {
        return 90 + 48 * 5
      }
    }
  },
  methods: {
    clickTab (name) {
      if (name === 'name1') {
        this.resetting()
      }
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        auditRemark: '',
        approStatus: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    pageChange2 (index) {
      this.tablePageParam2.pageIndex = index
      this.getTableData2()
    },
    pageSizeChange2 (index) {
      this.tablePageParam2.pageSize = index
      this.getTableData2()
    },
    getShareContractData (tablePageParam, queryContractName, userIdStr, statusStr, flag) {
      shareContractData(tablePageParam, queryContractName, userIdStr, statusStr).then(res => {
        if (res.code === '00000') {
          if (flag === 'wait') {
            this.tableData = res.data.records
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
          } else {
            this.tableData2 = res.data.records
            this.tablePageParam2 = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getTableData () {
      this.statusStr = 'UNAPPROVED'
      this.userIdStr = transferToStr(this.userId)
      this.getShareContractData(this.tablePageParam, this.queryContractName, this.userIdStr, this.statusStr, 'wait')
    },
    getTableData2 () {
      // status数组拼装成“，”连接的字符串statusStr,已审核的所有数据
      this.statusStr2 = ''
      if (this.status2.length === 0) {
        this.options.forEach(el => {
          this.statusStr2 = this.statusStr2 === '' ? el.value + ',' : this.statusStr2 + el.value + ','
        })
        this.statusStr2 = this.statusStr2.substring(0, this.statusStr2.length - 1)
      } else {
        this.statusStr2 = transferToStr(this.status2)
      }
      this.userIdStr2 = transferToStr(this.userId2)
      this.getShareContractData(this.tablePageParam2, this.queryContractName2, this.userIdStr2, this.statusStr2, 'has')
    },
    searchList () { this.getTableData() },
    searchList2 () { this.getTableData2() },
    // 查询租户下拉框
    searchUserList () {
      getUserData(0).then(res => {
        this.userListData = res.data
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getShareContractDetail (shareId) {
      shareContractDetail(shareId).then(res => {
        if (res.code === '00000') {
          this.arrDetails = res.data
          this.shareTenantList = res.data.shareTenantList
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 审批弹窗
    ApproDetails (index) {
      // 状态和审批描述置空(X掉弹窗后，置空上一次的操作。)
      this.languageType = this.tableData[index].languageType
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
      this.bizId = this.tableData[index].bizId
      this.modalAppro = true
      this.appModal = '共享审批—' + this.tableData[index].contractName + '合约'
      this.getShareContractDetail(this.tableData[index].shareId)
    },
    ok (name) {
      this.$refs[name].validate((valid) => {
        this.loadingStatus = true
        this.contype = 'CONTRACT_SHARE'
        if (!this.formItem.approStatus) {
          this.loadingStatus = false
        } else {
          let cppname = ''
          let useMemory = ''
          let languageType = ''
          let uploadVersion = ''
          let auditFile = []
          contractApprovel(this.bizId, this.contype, this.formItem.approStatus, this.formItem.auditRemark, cppname, useMemory, languageType, uploadVersion, auditFile).then(res => {
            if (res.code === '00000') {
              this.statusMsg = '审核通过'
              this.formItem.approStatus = ''
              this.formItem.auditRemark = ''
              this.loadingStatus = false
            } else {
              this.loadingStatus = false
              // this.msgInfo('error', res.message, true)
            }
            this.tipInfo(res)
          }).catch(error => {
            this.loadingStatus = false
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getTableData()
        this.getTableData2()
        this.modalAppro = false
      } else {
        // console.log('tipInfo-error:', res.message)
        this.msgInfo('error', res.message, true)
      }
    },
    cancelApp (name) {
      this.init()
      this.modalAppro = false
      // 用户操作清空，状态和审批描述置空
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
    },
    cancelDet (name) {
      this.init()
      this.modalDetail = false
    },
    // 详情弹窗
    ShowDetails (index) {
      this.languageType = this.tableData2[index].languageType
      this.bizId = this.tableData2[index].bizId
      this.modalDetail = true
      this.appModal = '共享审批—' + this.tableData2[index].contractName + '合约'
      this.getShareContractDetail(this.tableData2[index].shareId)
    },
    resetting () {
      this.queryContractName = ''
      this.status = ['UNAPPROVED']
      this.userId = []
      this.getTableData()
    },
    resetting2 () {
      this.queryContractName2 = ''
      this.status2 = ''
      this.userId2 = []
      this.getTableData2()
    }
  },
  mounted () {
    this.getTableData()
    this.getTableData2()
    this.searchUserList()
  }
}
</script>

<style lang="less" scoped>
.contractApp {
  /deep/.ivu-tabs {
    min-height: calc(100vh - 208px);
  }
  .width-input {
    width: 15vw;
    min-width: 200px;
  }
}
input {
  margin: 0 0 10px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
.bt1 {
  margin-right: 10px;
}
.search-title {
  font-size: 12px;
}
.title {
  font-weight: bold;
  font-size: 16px;
}
.bs {
  text-indent: 10px;
  line-height: 15px;
  border-left: 5px solid #3d73ef;
  margin-bottom: 15px;
}
.bg1 {
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  background-image: url("../../../assets/img/pass.png");
}
.bg2 {
  //以下是右下角图片设置
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  width: 100%;
  height: 140px;
  background-image: url("../../../assets/img/unpass.png");
}
.resultS {
  margin-top: 30px;
  font-weight: bold;
  font-size: 18px;
  color: #52c7aa;
  margin-left: 5px;
}
.resultF {
  margin-top: 20px;
  font-weight: bold;
  font-size: 18px;
  color: #ef7d68;
  margin-left: 5px;
}
.divS {
  margin-top: 10px;
  width: 750px;
  height: 70px;
  background-color: rgb(255, 255, 255);
}
/deep/.ivu-modal-footer {
  text-align: right;
}
/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 45vh;
  overflow: auto;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse > .ivu-collapse-item {
  border-radius: 5px 5px;
  margin-bottom: 10px;
}

/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}
</style>
