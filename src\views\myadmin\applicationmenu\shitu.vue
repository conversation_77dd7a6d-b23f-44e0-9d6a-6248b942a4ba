<template>
  <!-- 视图中心 -->
  <div>
    <div class="cz_header">
      <Input style="width:15%;margin-right:10px;" placeholder="可输入标题查询信息" v-model="search_value" @on-enter="getmanagementList" />
      <Select v-model="status" style="width:200px" clearable placeholder="请选择状态">
        <Option value="ENABLE">启用</Option>
        <Option value="DISABLE">禁用</Option>
      </Select>
      <Button type="primary" icon="ios-search" @click.native="getmanagementList">查询</Button>
      <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      <Button style="margin-left:10px;float:right;" type="success" :disabled="hasEditPermission" ghost @click="addsetting" icon="md-add">新建应用</Button>
    </div>
    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData">
        <template slot-scope="{ row, index }" slot="action">
          <Button size="small" :style="buttonStyle" :disabled="hasEditPermission" @click="edit(row)">修改</Button>
          <!-- <Poptip title="请确认是否删除？" confirm v-show="row.status == '禁用'" @on-ok="remove(row.id)"> -->
          <Button size="small" v-show="row.status == '禁用'" :disabled="hasEditPermission" :style="buttonStyle" @click="remove(row.id)">删除</Button>
          <!-- </Poptip> -->
          <!-- <Button size="small" style="margin-right: 5px;color:#3D73EF;border:1px solid #3D73EF;" @click="remove(row.id)" >删除</Button> -->
        </template>
      </Table>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" show-sizer show-total show-elevator class="paging" @on-change="changepage" :current.sync="tablePageParam.pageIndex" style="text-align: right;margin-top:20px;" @on-page-size-change="pageSizeChange" :pageSizeOpts="pageSizeOpts"></Page>
    </div>
    <Modal v-model="modal1" :mask-closable="false" :title="modal1title" @on-cancel="closeAll">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
        <!-- <FormItem label="标题:" prop="title">
          <Input v-model.trim="formValidate.title" class="textinput" show-word-limit maxlength="64" placeholder="请输入标题"></Input>
        </FormItem> -->
        <FormItem label="标题:" prop="title">
          <Select v-model="formValidate.title" placeholder="请选择标题">
            <Option value="中移链生产运行驾驶舱">中移链生产运行驾驶舱</Option>
            <Option value="中移链运营总览视图">中移链运营总览视图</Option>
            <Option value="中移链生产运维总览视图">中移链生产运维总览视图</Option>
          </Select>
        </FormItem>
        <FormItem label="描述:" prop="brief">
          <Input v-model.trim="formValidate.brief" class="valueinput input_reset_css" show-word-limit maxlength="1024" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请输入描述"></Input>
        </FormItem>
        <FormItem label="版本:" prop="version" tabindex="0">
          <Input v-model.trim="formValidate.version" class="textinput" show-word-limit maxlength="64" placeholder="请输入版本"></Input>
        </FormItem>
        <FormItem label="URL:" prop="routeUrl">
          <Input v-model.trim="formValidate.routeUrl" show-word-limit class="textinputUrl" maxlength="1000" placeholder="请输入url"></Input>
        </FormItem>
        <FormItem label="备注:" prop="remark">
          <Input v-model.trim="formValidate.remark" show-word-limit class="textinputMark" maxlength="128" placeholder="请输入备注"></Input>
        </FormItem>
        <FormItem label="可见租户:">
          <span class="lessee"> 仅平台管理员可见 </span>
          <RadioGroup v-model="formValidate.isPlatformVisible" @on-change='getRadio' style="margin-top:-5px">
            <Radio label="1">是</Radio>
            <Radio label="0">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="" prop="tenantIds" v-if="formValidate.isPlatformVisible=='0' ">
          <Select v-model="formValidate.tenantIds" placeholder="全部租户" multiple>
            <Option v-for="item in userListData" :value="item.id" :key="item.id">{{ item.tenantName}}</Option>
          </Select>
        </FormItem>
        <FormItem label="状态:" prop="status" v-if="modal1title==='修改应用'">
          <Select v-model="formValidate.status" placeholder="请选择状态">
            <Option value="ENABLE">启用</Option>
            <Option value="DISABLE">禁用</Option>
          </Select>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button size="small" @click="closeAll">取消</Button>
        <Button type="primary" size="small" @click="handleSubmit('formValidate')">确定</Button>
      </div>
    </Modal>
    <Modal v-model="modal3" @on-ok="detailConfig" @on-cancel="closemodal3" width=25>
      <p style="text-align: center;height:30px;margin-top:20px; line-height: 30px;">请确认是否删除</p>
    </Modal>
    <!-- <Modal v-model="modal3" :styles="{top: '250px'}">
      <p style="text-align:center;">请确认是否删除?</p>
      <div slot="footer">
        <Button size="small" @click="closemodal3">取消</Button>
        <Button type="primary" size="small" @click="detailConfig(formValidate)">确定</Button>
      </div>
    </Modal> -->
  </div>
</template>
<script>
import { alicationTable, alicationAdd, alicationUpdate, alicationDelete, getTenantList } from '@/api/contract'
import { localRead } from '@/lib/util'
export default {
  data () {
    return {
      deleId: '',
      pageSizeOpts: [10, 20, 40, 60, 100],
      formValidate: {// form表单
        id: '',
        title: '',
        brief: '',
        version: '',
        routeUrl: '',
        remark: '',
        tenantIds: [0],
        status: '',
        isPlatformVisible: '0'
      },
      modal1title: '新建应用',
      userListData: [],
      ruleValidate: {// 字段规则
        title: [
          { required: true, message: '标题为必填', trigger: 'change' }
        ],
        brief: [
          { required: true, message: '描述为必填', trigger: 'blur' }
        ],
        version: [
          { required: true, message: '版本为必填', trigger: 'blur' }
        ],
        routeUrl: [
          { required: true, message: 'URL为必填', trigger: 'blur' }
        ],
        remark: [
          { message: '', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态为必填', trigger: 'blur' }
        ]
      },
      modal1: false, // 弹窗
      modal3: false,
      status: '', // 搜索下拉框值
      dataCount: 0, // 总条数
      search_value: '', // 输入框
      tablePageParam: { pageIndex: 1, pageSize: 10 }, // 分页
      historyColumns: [//   table 表头
        {
          title: '标题',
          key: 'title',
        },
        {
          title: '描述',
          key: 'brief',
        },
        {
          title: '版本',
          key: 'version',
        },
        {
          title: 'URL',
          key: 'routeUrl',
        },
        {
          title: '备注',
          key: 'remark',
        },
        {
          title: '状态',
          key: 'status'
        },
        {
          title: '操作',
          key: 'resultCode',
          slot: 'action',
          width: '150'
        }
      ],
      // 表格数据
      historyData: [],
      isedit: false,
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  // 在 Vue 组件中
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  mounted () {
    this.getmanagementList()
    this.gettenantlist()
    console.log(this.userPermission.isOperationsAdmin);


  },

  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    getRadio (e) {
      this.formValidate.isPlatformVisible = e
      this.formValidate.tenantIds = ''
    },
    // 获取租户列表
    gettenantlist () {
      getTenantList().then(res => {
        if (res.code !== '00000') {
          this.msgInfo('warning', res.message, true)
        } else {
          this.userListData = res.data
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询列表接口
    getmanagementList () {
      let params = {
        title: this.search_value, // 键名
        status: this.status, // 状态
        pageParam: this.tablePageParam, // 分页
        appType: 'WEB_CENTER'//视图中心
      }
      alicationTable(params).then((res) => {
        if (res.code === '00000') {
          this.dataCount = res.data.total
          let status = {
            'ENABLE': '启用',
            'DISABLE': '禁用'

          }
          let data = res.data.records.map((item) => {
            return {
              ...item,
              status: status[item.status]
            }
          })
          this.historyData = data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 打开新增配置弹窗
    addsetting () {
      this.isedit = false
      this.modal1title = '新建应用'
      this.modal1 = true
      this.$refs.formValidate.resetFields()
      this.formValidate.isPlatformVisible = '0'
      this.formValidate.tenantIds = [0]
    },
    // 表单验证-新增或编辑
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.modal1title === '新建应用') {
            let addForm = {
              title: this.formValidate.title,
              brief: this.formValidate.brief,
              remark: this.formValidate.remark,
              version: this.formValidate.version,
              routeUrl: this.formValidate.routeUrl,
              tenantIds: this.formValidate.tenantIds.length ? this.formValidate.tenantIds : [0],
              isPlatformVisible: this.formValidate.isPlatformVisible,
              appType: 'WEB_CENTER'//视图中心
            }
            alicationAdd(addForm).then((res) => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.modal1 = false
                this.getmanagementList()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          } else {
            alicationUpdate(this.formValidate).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.modal1 = false
                this.getmanagementList()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          }
        }
      })
    },
    // 编辑配置
    edit (row) {

      let statusA = {
        '启用': 'ENABLE',
        '禁用': 'DISABLE'
      }
      this.isedit = true
      this.modal1title = '修改应用'
      this.formValidate = { ...row, status: statusA[row.status], appType: 'WEB_CENTER' }
      this.modal1 = true
    },
    // 删除
    remove (id) {
      this.modal3 = true
      this.deleId = id
    },
    // 确定删除
    detailConfig (param) {
      alicationDelete(this.deleId, 'WEB_CENTER').then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.getmanagementList()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 取消删除
    closemodal3 () {
      this.modal3 = false
    },
    // 关闭所有弹窗清空所有值
    closeAll () {
      if (this.modal1title === '新建应用') {
        this.modal1 = false
        this.$refs.formValidate.resetFields()
        this.formValidate.id = ''
      }
      this.modal1 = false
    },
    // 翻页
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getmanagementList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getmanagementList()
    },
    // 重置按钮事件
    reset () {
      this.search_value = ''
      this.status = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getmanagementList()
    },
    tabsFun (param) {
      if (param === 'name2') {
        this.getmanagementList()
      }
    },
  },
  watch: {
    'formValidate.tenantIds': {
      handler (newName, oldName) {
        if (!newName.length) {
          this.formValidate.tenantIds = [0]
        }
      },
      immediate: true,
      deep: true
    }
  }

}
</script>

<style lang="less" scoped>
/deep/ .valueinput {
  textarea.ivu-input {
    padding-bottom: 15px;
    min-height: 80px !important;
    height: 120px !important;
    overflow-y: scroll !important;
  }
}
.cz_header {
  // display: flex;
  margin-top: 10px;
  /deep/ .ivu-select,
  /deep/ .ivu-date-picker {
    width: 15%;
    margin-right: 10px;
  }
}

//
/deep/ .ivu-modal {
  width: 700px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
//
.cz_gjz {
  height: 33px;
  line-height: 32px;
  width: 99px;
  text-align: center;
  border: 1px solid;
  background: #2d8cf0;
  color: #fff;
}
.ivu-btn-primary {
  margin-left: 7px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 50%;
}
/deep/ .ivu-table-wrapper {
  overflow: visible;
}

/deep/ .ivu-btn-small:hover {
  background-color: rgba(61, 115, 239, 0.8) !important;
  color: #fff !important;
}
/deep/ .ivu-btn-small:active {
  background-color: #3d73ef;
}

/deep/.ivu-tooltip-inner {
  max-width: 400px;
  z-index: 10000;
}
/deep/ .ivu-table-overflowX {
  overflow-x: hidden;
}
/deep/ .ivu-table:before {
  display: none;
}
.textinput {
  /deep/.ivu-input {
    padding-right: 47px;
  }
}
.textinputUrl {
  /deep/.ivu-input {
    padding-right: 60px;
  }
}
.textinputMark {
  /deep/.ivu-input {
    padding-right: 53px;
  }
}
// 弹窗样式
.input_reset_css {
  border: 1px solid #dcdee2;
  padding-bottom: 20px;
  border-radius: 4px;
  /deep/ .ivu-input {
    border: none !important;
  }
  /deep/ .ivu-input:focus {
    border: none !important;
    box-shadow: 0px 0px 0px #fff !important;
  }
}

.input_reset_css_focus {
  border-color: #57a3f3;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
/deep/ .ivu-form-item-error {
  .input_reset_css {
    border: 1px solid red;
  }
  // .input_reset_css{
  //     box-shadow:0 0 0 2px rgba(237,64,20,0.2);
  // }
  .input_reset_css_focus {
    //  border: 1px solid red;
    box-shadow: 0 0 0 2px rgba(237, 64, 20, 0.2);
  }
}
.lessee {
  display: inline-block;
  margin-right: 10px;
}
</style>
<style lang="less">
.an {
  background: #b5f02d;
}
// .ivu-tooltip-popper{
//     max-height: 400px!important;
//     overflow-y:auto;
// }
.ivu-tooltip-contentr {
  max-height: 400px;
  overflow-y: auto;
}
.ivu-tooltip-inner {
  max-height: 300px;
  overflow-y: auto;
}
</style>
