<template>
  <div class="chainapprovel">
    <Tabs :value="name" @on-click="clickTab">
      <TabPane label="待审批" name="name1">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入链账户名称" style="vertical-align:baseline;" v-model="chainAccountName" />
          <Select class='bt1 width-input' v-model="tenantIdList" placeholder="请选择租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
        </p>
        <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
      </TabPane>
      <TabPane label="已审批" name="name2">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入链账户名称" style="vertical-align:baseline;" v-model="chainAccountName2" />
          <Select class='bt1 width-input' v-model="tenantIdList2" placeholder="请选择租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Select class='bt1 width-input' v-model="statusList2" placeholder="请选择状态" multiple :max-tag-count="2">
            <Option v-for="item in options" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList2">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting2">重置</Button>
        </p>
        <Table :columns="columns2" :data="tableData2">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF" @click="detailbtn(row)">详情</Button>

            <Button type="text" size="small" :disabled="hasEditPermission?true:!((row.statusMsg === '上链失败' || row.statusMsg === '审核通过')&&row.show===false)" :style="hasEditPermission?'color:#c5c8ce;border:1px solid #c5c8ce':(row.statusMsg === '上链失败' || row.statusMsg === '审核通过')&&row.show===false ? 'color:#3D73EF;border:1px solid #3D73EF' : 'color:#c5c8ce;border:1px solid #c5c8ce'" @click="appretry(row)">重试<span
                v-if="row.retrySurplusTime!==0&&row.show===true">({{row.retrySurplusTime}})</span></Button>
          </template>
        </Table>
        <Page :total="tablePageParam2.pagetotal" :current.sync="tablePageParam2.pageIndex" @on-change="pageChange2" :page-size="tablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;line-height:40px" />
      </TabPane>
    </Tabs>
    <Modal v-model="modalAppro" :title="appModal" width="650" :draggable=true sticky :mask-closable="false">
      <Card dis-hover>
        <p class="title bs"> 链账户信息 </p>
        <span style="line-height:30px">链账户名称：{{ arrDetails.chainAccountName }}</span><br>
        <span style="line-height:30px">类型：{{ arrDetails.type }}</span><br>
        <span style="line-height:30px">创建时间：{{ arrDetails.createTime }}</span><br>
        <span style="line-height:30px">更新时间：{{ arrDetails.updateTime }}</span><br>
        <span style="word-break:break-all;white-space: pre-wrap;">描述：{{ arrDetails.brief }}</span><br>
        <div v-if="arrDetails.type==='普通链账户'">
          <span style="line-height:30px">是否使用内存：{{arrDetails.useMemory==='y'?'是':'否'}}</span><br>
          <span v-if="arrDetails.useMemory==='y'" style="line-height:30px">使用内存量：{{arrDetails.useMemoryAmount}}&nbsp;Byte</span><br v-if="arrDetails.useMemory==='y'">
          <span style="line-height:30px;word-break:break-all;white-space: pre-wrap;">理由：{{arrDetails.reason}}</span><br>
        </div>

      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="arrDetails.type === '合约链账户'">
        <p class="title bs"> 运维信息 </p>
        <div v-if="arrDetails.ops && arrDetails.ops !== undefined">
          <p style="line-height:30px">
            合约类型：{{ arrDetails.ops.contractTypeDesc }}
          </p>
          <p style="line-height:30px"> TPS预估：{{ arrDetails.ops.tps }} </p>
          <p style="line-height:30px"> 运维联系人：
            <span v-if="arrDetails.ops.opsLinkman.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.opsLinkman.tenantName }}</span>
            <span v-if="arrDetails.ops.opsLinkman.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.opsLinkman.name }}</span>
            <span v-if="arrDetails.ops.opsLinkman.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.opsLinkman.phone }}</span>
          </p>
          <p style="line-height:30px"> 需求联系人：
            <span v-if="arrDetails.ops.demandSide.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.demandSide.tenantName }}</span>
            <span v-if="arrDetails.ops.demandSide.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.demandSide.name }}</span>
            <span v-if="arrDetails.ops.demandSide.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.demandSide.phone }}</span>
          </p>
          <p style="line-height:30px">
            <span style="vertical-align: top;line-height:30px">调用联系人：</span>
          <ul style="display:inline-block;line-height:30px;margin-left:4px;" v-if="arrDetails.ops.caller && arrDetails.ops.caller !==undefined">
            <li v-for="item in arrDetails.ops.caller" :key="item.name + 's'">
              <span v-if="item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
              <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
              <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
            </li>
          </ul>
          </p>
        </div>
      </Card>
      <div slot="footer">
        <Card shadow :bordered="false">
          <p class="title" style="text-align:left;">审批意见</p>
          <i-Form :model="formItem" :rules="formItemRule" :label-width="80" ref="formItem" style="text-align:left;">
            <FormItem style="margin:1px;padding:1px;" label="是否同意" prop="approStatus">
              <RadioGroup v-model="formItem.approStatus">
                <Radio label="APPROVED">同意</Radio>
                <Radio label="REJECT">不同意</Radio>
              </RadioGroup>
            </FormItem>
            <!-- <FormItem  style="padding-top:20px;" label="审批说明" prop="auditRemark">
            <Input v-model="auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）" :maxlength="30" show-word-limit></Input>
          </FormItem> -->
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" prop="auditRemark" v-show="formItem.approStatus==='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" v-show="formItem.approStatus!=='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）" :maxlength="30" show-word-limit></Input>
            </FormItem>
          </i-Form>
          <Button type="primary" @click="ok('formItem')" :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交" }}</Button>
          <Button type="default" @click="cancelApp('formItem')">取消</Button>
        </Card>
      </div>
    </Modal>
    <Modal v-model="modalDetail" :title="appModal" width="650" :draggable=true :mask-closable="false" sticky>
      <Card dis-hover>
        <p class="title bs"> 链账户信息 </p>
        <span style="line-height:30px">链账户名称：{{ arrDetails.chainAccountName }}</span><br>
        <span style="line-height:30px">类型：{{ arrDetails.type }}</span><br>
        <span style="line-height:30px">创建时间：{{ arrDetails.createTime }}</span><br>
        <span style="line-height:30px">更新时间：{{ arrDetails.updateTime }}</span><br>
        <span style="line-height:30px;word-break:break-all;white-space: pre-wrap;">描述：{{ arrDetails.brief }}</span><br>
        <div v-if="arrDetails.type==='普通链账户'">
          <span style="line-height:30px">是否使用内存：{{arrDetails.useMemory==='y'?'是':'否'}}</span><br>
          <span v-if="arrDetails.useMemory==='y'" style="line-height:30px">使用内存量：{{arrDetails.useMemoryAmount}}&nbsp;Byte</span><br v-if="arrDetails.useMemory==='y'">
          <span style="line-height:30px;word-break:break-all;white-space: pre-wrap;">理由：{{arrDetails.reason}}</span><br>
        </div>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="arrDetails.type === '合约链账户'">
        <p class="title bs"> 运维信息 </p>
        <div v-if="arrDetails.ops && arrDetails.ops !== undefined">
          <p style="line-height:30px">
            合约类型：{{ arrDetails.ops.contractTypeDesc }}
          </p>
          <p style="line-height:30px"> TPS预估：{{ arrDetails.ops.tps }} </p>
          <p style="line-height:30px"> 运维联系人：
            <span v-if="arrDetails.ops.opsLinkman.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.opsLinkman.tenantName }}</span>
            <span v-if="arrDetails.ops.opsLinkman.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.opsLinkman.name }}</span>
            <span v-if="arrDetails.ops.opsLinkman.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.opsLinkman.phone }}</span>
          </p>
          <p style="line-height:30px"> 需求联系人：
            <span v-if="arrDetails.ops.demandSide.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.demandSide.tenantName }}</span>
            <span v-if="arrDetails.ops.demandSide.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.demandSide.name }}</span>
            <span v-if="arrDetails.ops.demandSide.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.demandSide.phone }}</span>
          </p>
          <p style="line-height:30px">
            <span style="vertical-align: top;line-height:30px">调用联系人：</span>
          <ul style="display:inline-block;line-height:30px;margin-left:4px;" v-if="arrDetails.ops.caller && arrDetails.ops.caller !==undefined">
            <li v-for="item in arrDetails.ops.caller" :key="item.name + 'ss'">
              <span v-if="item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
              <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
              <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
            </li>
          </ul>
          </p>
        </div>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="this.deployStatus==='上链失败'">
        <p class="title bs"> 失败原因 </p>
        <p v-if="whyDetail.code===''||whyDetail.code===null">{{whyDetail.errorInfo===null?'':whyDetail.errorInfo}}</p>
        <div v-else>
          <span style="line-height:30px">编码信息：{{whyDetail.code}}</span><br>
          <span style="line-height:30px">失败原因：{{ !isNaN(whyDetail.code)?whyDetail.what+'，请联系管理员':whyDetail.what  }}</span><br>
          <span style="line-height:30px">方法：{{whyDetail.name}}</span><br>
        </div>

      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-else-if="this.deployStatus==='上链成功'">
        <p class="title bs"> 成功信息 </p>
        <span v-if="whyDetail.transactionId!==''" style="line-height:30px">交易ID：{{whyDetail.transactionId===null?'':whyDetail.transactionId}}</span><br>
      </Card>
      <div slot="footer" class="bg1" :bordered="false" v-show="arrDetails.status !== '审核拒绝'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left; margin-left:5px; margin-top:10px;">结果:<span class="resultS">{{ arrDetails.lastAuditStatusDesc }}</span></p>
          <p style="text-align:left; margin-left:5px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
      <div slot="footer" class="bg2" :bordered="false" v-show="arrDetails.status === '审核拒绝'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left; margin-left:5px; margin-top:10px;">结果:<span class="resultF">{{ arrDetails.lastAuditStatusDesc }}</span></p>
          <p style="text-align:left; margin-left:5px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getChainsData, getUserData, contractApprovel, getChainApprDetails } from '@/api/data'
import { ExaminationApproval } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import { localRead } from '@/lib/util'
export default {
  name: 'chain-approvel',
  components: {
    EditTableMul
  },
  data () {
    return {
      loadingStatus: false,
      name: this.$route.params.tabs || 'name1',
      caller: [],
      ops: {},
      // nullData: false,
      modalAppro: false,
      modalDetail: false,
      chainAccountName: '',
      chainAccountName2: '',
      userIdStr: '',
      userIdStr2: '',
      formItem: {
        auditRemark: '',
        approStatus: ''
      },
      formItemRule: {
        approStatus: [{ required: true, message: '请选择是否同意！', trigger: 'change' }],
        auditRemark: [{ required: true, message: '请填写审批说明！', trigger: 'blur' }]
      },
      appModal: '链账户审批',
      arrDetails: {},
      statusList: ['UNAPPROVED'],
      statusStr: '',
      tenantIdList: [],
      userListData: [],
      statusList2: [],
      statusStr2: '',
      tenantIdList2: [],
      bizId: 0,
      options: [{
        value: 'APPROVED',
        label: '审核通过'
      }, {
        value: 'REJECT',
        label: '审核拒绝'
      }, {
        value: 'CHAIN_SUCCESS',
        label: '上链成功'
      }, {
        value: 'CHAIN_FAILED',
        label: '上链失败'
      }],

      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        { key: 'submitName', title: '平台账户' },
        { key: 'chainAccountName', title: '链账户名称' },
        { key: 'ownerTenantName', title: '租户' },
        { key: 'chainName', title: '链名称' },
        {
          key: 'statusMsg',
          title: '状态',
          // width: 160,
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.statusMsg === '待审核' ? 'primary' : row.statusMsg === '审核通过' ? '#2db7f5' : row.statusMsg === '审核拒绝' ? 'error' : row.statusMsg === '上链成功' ? 'success' : '#515a6e'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.statusMsg)
          }
        },
        // { key: 'auditName',
        //   title: '审批人',
        //   tooltip: true,
        //   render: (h, params) => {
        //     return h('span', params.row.auditName === null ? '/' : params.row.auditName)
        //   }
        // },
        {
          key: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          width: 150,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '审批')

            ])
          }
        }
      ],

      tableData: [],
      tablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns2: [
        { key: 'submitName', title: '平台账户' },
        { key: 'chainAccountName', title: '链账户名称' },
        { key: 'ownerTenantName', title: '租户' },
        { key: 'chainName', title: '链名称' },
        {
          key: 'statusMsg',
          title: '状态',
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.statusMsg === '待审核' ? 'primary' : row.statusMsg === '审核通过' ? '#2db7f5' : row.statusMsg === '审核拒绝' ? 'error' : row.statusMsg === '上链成功' ? 'success' : '#515a6e'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';

            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.statusMsg)
          }
        },
        { key: 'auditName', title: '审批人', tooltip: true },
        {
          slot: 'action',
          title: '操作',
          width: 150,
          // fixed: 'right',
          align: 'left',
        }
      ],
      tableData2: [],
      deployStatus: '',
      whyDetail: {},
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  methods: {
    // 重试
    appretry (value) {
      let data = {
        bizId: value.bizId,
        type: 'CHAIN_ACCOUNT_CREATE'
      }
      ExaminationApproval(data).then(res => {
        if (res.code === '00000') {
          this.msgInfo('info', res.message, true)
          this.getTableData2()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickTab (name) {
      if (name === 'name1') {
        this.resetting()
      }
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        auditRemark: '',
        approStatus: ''
      }
      this.statusList2 = []
      this.tenantIdList2 = []
      this.chainAccountName2 = ''
      this.tenantIdList = []
      this.chainAccountName = ''
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    pageChange2 (index) {
      this.tablePageParam2.pageIndex = index
      this.getTableData2()
    },
    pageSizeChange2 (index) {
      this.tablePageParam2.pageSize = index
      this.getTableData2()
    },
    detailbtn (row) {
      // console.log('进入查看详情弹窗函数')
      this.deployStatus = row.statusMsg
      this.bizId = row.bizId
      getChainApprDetails(row.chainAccountId).then(res => {
        if (res.code === '00000') {
          this.modalDetail = true
          this.arrDetails = res.data
          this.whyDetail = res.data.message ? res.data.message : {}
          this.caller = []
          if (res.data.ops && res.data.ops.caller) {
            this.caller = res.data.ops.caller
            this.ops = res.data.ops
          } else if (res.data.ops) {
            this.ops = res.data.ops
            // console.log('this.ops:', this.ops)
          } else {
            this.nullData = true
            this.ops = {
              contractTypeDesc: '无',
              tps: '0',
              demandSide: {
                tenantName: '无',
                name: '无',
                phone: '无'
              },
              opsLinkman: {
                tenantName: '无',
                name: '无',
                phone: '无'
              },
              caller: {
                tenantName: '无',
                name: '无',
                phone: '无'
              }
            }
          }
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },

    getTableData () {
      // 查询待审核状态的数据
      this.statusStr = 'UNAPPROVED'

      this.userIdStr = ''
      for (var j = 0; j < this.tenantIdList.length; j++) {
        if (j < this.tenantIdList.length - 1) {
          this.userIdStr += this.tenantIdList[j] + ','
        } else {
          this.userIdStr += this.tenantIdList[j]
        }
      }
      getChainsData(this.tablePageParam, this.chainAccountName, this.userIdStr, this.statusStr).then(res => {
        // console.log('getTableData===>', res)
        if (res.code === '00000') {
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('getTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getTableData2 () {
      // status数组拼装成“，”连接的字符串statusStr,已审核的所有数据
      this.statusStr2 = ''
      if (this.statusList2.length === 0) {
        this.statusStr2 = 'APPROVED,REJECT,CHAIN_SUCCESS,CHAIN_FAILED'
      } else {
        for (var i = 0; i < this.statusList2.length; i++) {
          if (i < this.statusList2.length - 1) {
            this.statusStr2 += this.statusList2[i] + ','
          } else {
            this.statusStr2 += this.statusList2[i]
          }
        }
      }

      this.userIdStr2 = ''
      for (var j = 0; j < this.tenantIdList2.length; j++) {
        if (j < this.tenantIdList2.length - 1) {
          this.userIdStr2 += this.tenantIdList2[j] + ','
        } else {
          this.userIdStr2 += this.tenantIdList2[j]
        }
      }

      getChainsData(this.tablePageParam2, this.chainAccountName2, this.userIdStr2, this.statusStr2).then(res => {
        // console.log('getTableData===>', res)
        if (res.code === '00000') {
          this.tableData2 = res.data.records
          this.tablePageParam2 = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.tableData2.forEach((item) => {
            this.$set(item, 'show', false)
            if (item.retrySurplusTime !== null) {
              this.cratetimer(item)
            }
          })
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('getTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    cratetimer (item) {
      // let TIME_COUNT = time
      // if (!this.timer) {
      // thicount = item.retrySurplusTime
      // item.show = false
      let timer = setInterval(() => {
        if (item.retrySurplusTime > 0) {
          item.retrySurplusTime--
          item.show = true
        } else {
          item.show = false
          // this.getTableData2()
          clearInterval(timer)
          // this.timer = null
        }
      }, 1000)
      // }
    },
    searchList () { this.getTableData() },
    searchList2 () { this.getTableData2() },

    // 查询租户下拉框
    searchUserList () {
      getUserData(0).then(res => {
        this.userListData = res.data
      }).catch(error => {
        // console.log('getUrserData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 审批弹窗
    ApproDetails (index) {
      // console.log('进入弹窗函数')
      // 状态和审批描述置空(X掉弹窗后，置空上一次的操作。)
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''

      this.bizId = this.tableData[index].bizId
      getChainApprDetails(this.tableData[index].chainAccountId).then(res => {
        if (res.code === '00000') {
          this.modalAppro = true
          this.arrDetails = res.data
          this.caller = []
          if (res.data.ops && res.data.ops.caller) {
            this.caller = res.data.ops.caller
            this.ops = res.data.ops
          } else if (res.data.ops) {
            this.ops = res.data.ops
            // console.log('this.ops:', this.ops)
          } else {
            this.nullData = true
            this.ops = {
              contractTypeDesc: '无',
              tps: '0',
              demandSide: {
                tenantName: '无',
                name: '无',
                phone: '无'
              },
              opsLinkman: {
                tenantName: '无',
                name: '无',
                phone: '无'
              },
              caller: {
                tenantName: '无',
                name: '无',
                phone: '无'
              }
            }
          }
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    ok (name) {
      this.$refs[name].validate((valid) => {
        this.loadingStatus = true
        this.contype = 'CHAIN_ACCOUNT_CREATE'
        if (!this.formItem.approStatus) {
          this.loadingStatus = false
          // this.msgInfo('warning', '请选择是否同意！', true)
          this.loadingStatus = false
        } else {
          let cppname = ''
          let languageType = ''
          let uploadVersion = ''
          let auditFile = []
          contractApprovel(this.bizId, this.contype, this.formItem.approStatus, this.formItem.auditRemark, cppname, this.arrDetails.useMemory, languageType, uploadVersion, auditFile).then(res => {
            // console.log('addChainApproval===>', res)
            // this.msgInfo('info', res.message)
            this.tipInfo(res)
          }).catch(error => {
            this.loadingStatus = false
            // console.log('contractApprovel.error===>', error)
            this.msgInfo('error', error.message, true)
          })
          // 审批一条后，状态和审批描述置空
        }
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.loadingStatus = false
        this.getTableData()
        this.getTableData2()
        this.modalAppro = false
        this.formItem.approStatus = ''
        this.formItem.auditRemark = ''
      } else {
        this.loadingStatus = false
        // console.log('tipInfo-error:', res.message)
        this.msgInfo('error', res.message, true)
      }
    },
    cancelApp (name) {
      // this.init()
      this.modalAppro = false
      // 用户操作清空，状态和审批描述置空
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
    },
    cancelDet (name) {
      // this.init()
      this.modalDetail = false
    },
    resetting () {
      this.tenantIdList = []
      this.statusList = ['UNAPPROVED']
      this.chainAccountName = ''
      this.getTableData()
    },
    resetting2 () {
      this.tenantIdList2 = []
      this.statusList2 = []
      this.chainAccountName2 = ''
      this.getTableData2()
    }

  },
  computed: {


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  watch: {
    tableData: {
      handler (newVal) {
        //
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    this.getTableData()
    this.getTableData2()
    this.searchUserList()
  }

}
</script>

<style lang="less" scoped>
.chainapprovel {
  /deep/.ivu-tabs {
    min-height: calc(100vh - 208px);
  }
  .width-input {
    width: 15vw;
    min-width: 200px;
  }
}
input {
  margin: 0 0 10px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
.bt1 {
  margin-right: 10px;
}
.search-title {
  font-size: 12px;
}

.title {
  font-weight: bold;
  font-size: 16px;
}
.bs {
  text-indent: 10px;
  line-height: 15px;
  border-left: 5px solid #3d73ef;
  margin-bottom: 15px;
}
.bg1 {
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  background-image: url("../../../assets/img/pass.png");
}
.bg2 {
  //以下是右下角图片设置
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  width: 100%;
  height: 140px;
  background-image: url("../../../assets/img/unpass.png");
}
.resultS {
  margin-top: 30px;
  font-weight: bold;
  font-size: 18px;
  color: #52c7aa;
  margin-left: 5px;
}

.resultF {
  margin-top: 20px;
  font-weight: bold;
  font-size: 18px;
  color: #ef7d68;
  margin-left: 5px;
}
.divS {
  //margin-top:10px;
  //margin-left: 30px;
  width: 500px;
  height: 70px;
  background-color: rgb(255, 255, 255);
}
/deep/.ivu-modal-footer {
  /* border-top: 1px solid #e8eaec; */
  /* padding: 12px 18px 12px 18px; */
  text-align: right;
  //background-color: #F5F6FA;
}
/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 45vh;
  overflow: auto;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
